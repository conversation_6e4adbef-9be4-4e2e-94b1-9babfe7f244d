# GitHub Actions IAM 設定指南

## 概覽

本指南說明如何設定 `longshun-prod-github-actions` IAM role，讓 GitHub Actions 可以操作 ECR 和部署應用程式至 EKS。

## 功能特性

- ✅ **安全的 OIDC 認證**：使用 GitHub OIDC provider，無需長期憑證
- ✅ **ECR 完整權限**：支援 Docker image 的 push/pull 操作
- ✅ **EKS 部署權限**：可以存取 EKS 叢集進行應用程式部署
- ✅ **分支限制**：只允許特定分支使用此 role
- ✅ **Repository 限制**：限制特定的 GitHub repository
- ✅ **最小權限原則**：只授予必要的權限

## 快速設定

### 1. 啟用 GitHub Actions IAM

在 `terraform.tfvars` 中設定：

#### 選項 A：無 Repository 限制（推薦）

```hcl
# 啟用 GitHub Actions IAM
enable_github_actions_iam = true

# GitHub 設定
github_org = "your-github-org"
github_enable_repo_restrictions = false  # 允許組織內所有 repositories

# ECR 存取權限（可選，留空則允許存取所有 ECR）
github_actions_ecr_repository_arns = [
  "arn:aws:ecr:ap-east-2:************:repository/your-app"
]
```

#### 選項 B：啟用 Repository 限制

```hcl
# 啟用 GitHub Actions IAM
enable_github_actions_iam = true

# GitHub 設定
github_org = "your-github-org"
github_enable_repo_restrictions = true
github_repo = "your-app-repo"
github_allowed_branches = ["main", "develop"]

# ECR 存取權限（可選，留空則允許存取所有 ECR）
github_actions_ecr_repository_arns = [
  "arn:aws:ecr:ap-east-2:************:repository/your-app"
]
```

### 2. 部署基礎設施

```bash
terraform plan
terraform apply
```

### 3. 取得 Role ARN

部署完成後，取得 GitHub Actions role ARN：

```bash
terraform output github_actions_role_arn
```

## GitHub Actions Workflow 範例

### 基本 CI/CD Pipeline

```yaml
name: Deploy to EKS

on:
  push:
    branches: [main]

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          role-session-name: GitHubActions
          aws-region: ap-east-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: your-app
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Deploy to EKS
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: your-app
          IMAGE_TAG: ${{ github.sha }}
        run: |
          aws eks update-kubeconfig --region ap-east-2 --name longshun-tpe
          kubectl set image deployment/your-app your-app=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          kubectl rollout status deployment/your-app
```

### 多環境部署範例

```yaml
name: Multi-Environment Deploy

on:
  push:
    branches: [main, develop]

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: 
          - name: staging
            branch: develop
            cluster: longshun-staging
          - name: production
            branch: main
            cluster: longshun-prod
    
    if: github.ref == format('refs/heads/{0}', matrix.environment.branch)
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          role-session-name: GitHubActions-${{ matrix.environment.name }}
          aws-region: ap-east-2

      - name: Deploy to ${{ matrix.environment.name }}
        run: |
          aws eks update-kubeconfig --region ap-east-2 --name ${{ matrix.environment.cluster }}
          # 部署邏輯...
```

## GitHub Repository 設定

### 1. 設定 Secrets

在 GitHub repository 的 Settings > Secrets and variables > Actions 中加入：

- `AWS_ROLE_ARN`: GitHub Actions IAM role 的 ARN

### 2. 設定 Repository 權限

確保 repository 的 Actions 設定允許：
- ✅ Read repository contents
- ✅ Write to packages (如果使用 GitHub Packages)

## 安全最佳實踐

### 1. 分支保護

- 設定 branch protection rules
- 要求 pull request reviews
- 要求 status checks 通過

### 2. 權限最小化

```hcl
# 只允許特定的 ECR repositories
github_actions_ecr_repository_arns = [
  "arn:aws:ecr:ap-east-2:************:repository/app-frontend",
  "arn:aws:ecr:ap-east-2:************:repository/app-backend"
]

# 如果需要限制特定 repository 和分支
github_enable_repo_restrictions = true
github_repo = "your-app-repo"
github_allowed_branches = ["main"]
```

### 3. 監控和審計

- 啟用 CloudTrail 記錄 API 呼叫
- 設定 CloudWatch 警報監控異常活動
- 定期檢查 IAM role 的使用情況

## 故障排除

### 常見問題

#### 1. "Error: could not assume role"

**原因**：Trust relationship 設定錯誤

**解決方案**：
- 檢查 `github_org` 和 `github_repo` 設定是否正確
- 確認分支名稱在 `github_allowed_branches` 中
- 檢查 GitHub Actions 的 `permissions` 設定

#### 2. "Error: AccessDenied when calling ECR"

**原因**：ECR 權限不足

**解決方案**：
- 檢查 `github_actions_ecr_repository_arns` 設定
- 確認 ECR repository 存在且 ARN 正確

#### 3. "Error: Forbidden when accessing EKS"

**原因**：EKS 權限不足

**解決方案**：
- 確認 EKS 叢集名稱正確
- 檢查是否需要額外的 assume role 權限

### 除錯指令

```bash
# 檢查 role 是否存在
aws iam get-role --role-name longshun-prod-github-actions

# 檢查 role 的 trust policy
aws iam get-role --role-name longshun-prod-github-actions --query 'Role.AssumeRolePolicyDocument'

# 檢查附加的 policies
aws iam list-attached-role-policies --role-name longshun-prod-github-actions

# 測試 ECR 存取
aws ecr describe-repositories --region ap-east-2

# 測試 EKS 存取
aws eks describe-cluster --name longshun-tpe --region ap-east-2
```

## 進階配置

### 自訂 ECR 權限

```hcl
# 允許存取特定 ECR repositories
github_actions_ecr_repository_arns = [
  "arn:aws:ecr:${var.region}:${data.aws_caller_identity.current.account_id}:repository/app-*"
]
```

### 額外的 IAM 權限

```hcl
# 附加額外的 managed policies
github_actions_additional_policy_arns = [
  "arn:aws:iam::aws:policy/ReadOnlyAccess"
]

# 允許承擔其他 roles
github_actions_additional_assume_role_arns = [
  "arn:aws:iam::************:role/deployment-role"
]
```

## 相關資源

- [GitHub Actions OIDC 文檔](https://docs.github.com/en/actions/deployment/security-hardening-your-deployments/about-security-hardening-with-openid-connect)
- [AWS IAM OIDC 文檔](https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles_providers_create_oidc.html)
- [ECR 權限參考](https://docs.aws.amazon.com/AmazonECR/latest/userguide/security_iam_service-with-iam.html)
- [EKS 權限參考](https://docs.aws.amazon.com/eks/latest/userguide/security_iam_service-with-iam.html)
