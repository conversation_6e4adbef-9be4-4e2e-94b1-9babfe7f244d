# KMS 模組

## 概覽

此模組使用官方的 `terraform-aws-modules/kms/aws` 模組來創建和管理 AWS KMS (Key Management Service) 金鑰。主要用於 HashiCorp Vault 的自動解封功能，提供安全的金鑰管理和加密服務。

## 功能特性

- ✅ **KMS 金鑰管理**：創建和管理客戶管理的 KMS 金鑰
- ✅ **Vault 自動解封**：專為 Vault 自動解封設計
- ✅ **金鑰輪換**：啟用自動金鑰輪換
- ✅ **IAM 整合**：完整的 IAM 權限管理
- ✅ **別名支援**：友好的金鑰別名
- ✅ **權限控制**：細粒度的存取控制
- ✅ **標籤管理**：完整的資源標籤支援
- ✅ **安全刪除**：可配置的刪除等待期

## 架構圖

```mermaid
graph TB
    subgraph "AWS KMS"
        subgraph "客戶管理金鑰"
            KEY[KMS 金鑰<br/>vault-auto-unseal]
            ALIAS[金鑰別名<br/>alias/vault-auto-unseal]
        end
        
        subgraph "金鑰政策"
            ADMIN[管理員權限]
            OWNER[擁有者權限]
            USER[使用者權限]
        end
    end
    
    subgraph "IAM"
        POLICY[Vault KMS 政策]
        ROLE[Vault IAM 角色]
    end
    
    subgraph "EKS 叢集"
        subgraph "Vault Pod"
            VAULT[HashiCorp Vault]
        end
    end
    
    subgraph "使用案例"
        UNSEAL[自動解封]
        ENCRYPT[資料加密]
        DECRYPT[資料解密]
    end
    
    VAULT --> ROLE
    ROLE --> POLICY
    POLICY --> KEY
    
    KEY --> UNSEAL
    KEY --> ENCRYPT
    KEY --> DECRYPT
    
    ALIAS --> KEY
    
    ADMIN --> KEY
    OWNER --> KEY
    USER --> KEY
```

## 使用方式

### 基本使用

```hcl
module "kms" {
  source = "./modules/kms"

  tags = {
    Environment = "dev"
    Project     = "my-project"
    Purpose     = "vault-unseal"
  }
}
```

### 進階配置

```hcl
module "kms" {
  source = "./modules/kms"

  tags = {
    Environment        = "prod"
    Project           = "production-app"
    CostCenter        = "engineering"
    DataClassification = "confidential"
    Purpose           = "vault-unseal"
    Owner             = "security-team"
  }
}
```

## 輸入變數

| 名稱 | 描述 | 類型 | 預設值 | 必要 |
|------|------|------|--------|------|
| `tags` | 資源標籤 | `map(string)` | `{}` | ❌ |

## 輸出值

| 名稱 | 描述 |
|------|------|
| `kms_key_id` | KMS 金鑰 ID |
| `kms_key_arn` | KMS 金鑰 ARN |
| `kms_key_alias` | KMS 金鑰別名 |
| `kms_unseal_policy_arn` | Vault 解封 IAM 政策 ARN |

## KMS 金鑰配置

### 金鑰屬性
- **描述**: "Vault auto-unseal KMS key"
- **用途**: ENCRYPT_DECRYPT
- **金鑰規格**: SYMMETRIC_DEFAULT
- **別名**: "vault-auto-unseal"
- **刪除等待期**: 10 天
- **自動輪換**: 啟用

### 權限配置
```json
{
  "key_administrators": [
    "arn:aws:iam::{account-id}:root"
  ],
  "key_owners": [
    "arn:aws:iam::{account-id}:root"
  ],
  "key_users": [
    "arn:aws:iam::{account-id}:root"
  ]
}
```

## IAM 政策

此模組會創建一個 IAM 政策，允許 Vault 使用 KMS 金鑰進行自動解封：

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "kms:Encrypt",
        "kms:Decrypt",
        "kms:ReEncrypt*",
        "kms:GenerateDataKey*",
        "kms:DescribeKey"
      ],
      "Resource": "arn:aws:kms:region:account:key/key-id"
    }
  ]
}
```

## Vault 整合

### Vault 配置範例

```hcl
# vault.hcl
seal "awskms" {
  region     = "ap-east-2"
  kms_key_id = "alias/vault-auto-unseal"
}

storage "consul" {
  address = "127.0.0.1:8500"
  path    = "vault/"
}

listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = 1
}

api_addr = "http://127.0.0.1:8200"
cluster_addr = "https://127.0.0.1:8201"
ui = true
```

### Kubernetes 中的 Vault 配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: vault-config
data:
  vault.hcl: |
    seal "awskms" {
      region     = "ap-east-2"
      kms_key_id = "alias/vault-auto-unseal"
    }
    
    storage "raft" {
      path = "/vault/data"
    }
    
    listener "tcp" {
      address     = "0.0.0.0:8200"
      tls_disable = "true"
    }
    
    api_addr = "http://vault:8200"
    cluster_addr = "https://vault:8201"
    ui = true
```

## 最佳實踐

### 1. 安全性
- 使用最小權限原則
- 定期輪換金鑰
- 監控金鑰使用情況
- 啟用 CloudTrail 記錄

### 2. 權限管理
```hcl
# 為不同角色設置不同權限
key_administrators = [
  "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/KMSAdminRole"
]

key_users = [
  "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/VaultRole"
]
```

### 3. 監控和警報
- 設置 CloudWatch 警報監控金鑰使用
- 監控異常的加密/解密活動
- 設置金鑰刪除警報

### 4. 備份和災難恢復
- 記錄金鑰 ID 和別名
- 備份 Vault 配置
- 測試災難恢復程序

## 成本優化

### KMS 定價考量
- **金鑰費用**: 每個客戶管理金鑰 $1/月
- **API 調用**: 每 10,000 次請求 $0.03
- **自動輪換**: 每次輪換 $1

### 成本優化建議
1. 合理規劃金鑰數量
2. 監控 API 調用頻率
3. 評估自動輪換需求
4. 使用金鑰別名減少硬編碼

## 故障排除

### 常見問題

#### 1. Vault 無法使用 KMS 金鑰
**問題**：Vault 啟動時無法訪問 KMS 金鑰
```
Error initializing seal: error fetching key information: AccessDenied
```

**解決方案**：
- 檢查 IAM 角色權限
- 確認 KMS 金鑰政策
- 驗證 AWS 憑證配置

#### 2. 金鑰別名不存在
**問題**：找不到指定的金鑰別名
```
Error: alias/vault-auto-unseal is not found
```

**解決方案**：
```bash
# 檢查金鑰別名
aws kms list-aliases --region ap-east-2

# 檢查金鑰狀態
aws kms describe-key --key-id alias/vault-auto-unseal
```

#### 3. 權限不足
**問題**：IAM 角色沒有足夠權限

**解決方案**：
```bash
# 檢查 IAM 政策
aws iam get-role-policy --role-name VaultRole --policy-name VaultKMSPolicy

# 測試權限
aws kms describe-key --key-id alias/vault-auto-unseal
```

### 調試指令

```bash
# 檢查 KMS 金鑰
aws kms describe-key --key-id alias/vault-auto-unseal

# 檢查金鑰政策
aws kms get-key-policy --key-id alias/vault-auto-unseal --policy-name default

# 檢查 IAM 政策
aws iam list-attached-role-policies --role-name VaultRole

# 測試加密/解密
aws kms encrypt --key-id alias/vault-auto-unseal --plaintext "test"
```

## 安全考量

### 1. 金鑰管理
- 定期審查金鑰權限
- 監控金鑰使用模式
- 實施金鑰輪換策略

### 2. 存取控制
- 使用 IAM 角色而非用戶
- 實施最小權限原則
- 定期審查權限

### 3. 審計和合規
- 啟用 CloudTrail 記錄
- 監控 KMS API 調用
- 實施合規檢查

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0
- **terraform-aws-modules/kms/aws**: ~> 2.1.0

## 相關資源

- [AWS KMS 開發者指南](https://docs.aws.amazon.com/kms/latest/developerguide/)
- [terraform-aws-modules/kms/aws](https://registry.terraform.io/modules/terraform-aws-modules/kms/aws/latest)
- [HashiCorp Vault AWS KMS Seal](https://www.vaultproject.io/docs/configuration/seal/awskms)
- [KMS 最佳實踐](https://docs.aws.amazon.com/kms/latest/developerguide/best-practices.html)

## 範例

查看 [examples](../../examples/) 目錄以獲取更多使用範例。
