data "aws_caller_identity" "current" {}

# 使用官方 KMS 模組
module "kms" {
  source  = "terraform-aws-modules/kms/aws"
  version = "2.1.0"

  description = "Vault auto-unseal KMS key"
  key_usage   = "ENCRYPT_DECRYPT"

  # 金鑰設定
  deletion_window_in_days = 10
  enable_key_rotation     = true

  # 別名設定
  aliases = ["vault-auto-unseal"]

  # 金鑰存取權限
  key_administrators = [
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
  ]
  key_owners = [
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
  ]
  key_users = [
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
  ]

  # 標籤
  tags = var.tags
}

# 建立 IAM 政策，允許使用 KMS 金鑰進行 Vault 自動解封
resource "aws_iam_policy" "vault_kms_unseal" {
  name        = "vault-kms-unseal"
  description = "Allow access to KMS key for Vault auto-unseal"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:DescribeKey"
        ],
        Resource = module.kms.key_arn
      }
    ]
  })
}
