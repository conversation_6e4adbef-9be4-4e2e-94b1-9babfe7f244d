output "key_arn" {
  description = "KMS 金鑰 ARN"
  value       = try(module.kms.key_arn, null)
}

output "key_id" {
  description = "KMS 金鑰 ID"
  value       = try(module.kms.key_id, null)
}

output "kms_key_id" {
  description = "KMS 金鑰 ID用於 EKS Addon"
  value       = try(module.kms.key_id, null)
}

output "key_alias_arn" {
  description = "KMS key alias ARN"
  value       = try(module.kms.aliases["vault-auto-unseal"].arn, null)
}

output "key_alias_name" {
  description = "KMS key alias name"
  value       = try(module.kms.aliases["vault-auto-unseal"].name, null)
}

output "kms_unseal_policy_arn" {
  description = "Vault 解封政策 ARN"
  value       = try(aws_iam_policy.vault_kms_unseal.arn, null)
}

output "unseal_policy_name" {
  description = "Vault 解封政策名稱"
  value       = try(aws_iam_policy.vault_kms_unseal.name, null)
}
