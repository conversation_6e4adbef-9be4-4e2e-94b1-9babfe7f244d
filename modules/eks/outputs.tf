output "cluster_id" {
  description = "EKS 叢集 ID"
  value       = module.eks.cluster_id
}

output "cluster_endpoint" {
  description = "EKS 叢集端點"
  value       = module.eks.cluster_endpoint
}

output "cluster_security_group_id" {
  description = "EKS 叢集安全群組 ID"
  value       = module.eks.cluster_security_group_id
}

output "oidc_provider_arn" {
  description = "EKS OIDC Provider ARN"
  value       = module.eks.oidc_provider_arn
}

output "oidc_provider_url" {
  description = "EKS OIDC Provider Issuer URL"
  value       = module.eks.cluster_oidc_issuer_url
}


output "certificate_authority_data" {
  description = "EKS 叢集憑證授權資料"
  value       = module.eks.cluster_certificate_authority_data
}

output "node_security_group_id" {
  description = "EKS 節點安全群組 ID"
  value       = module.eks.node_security_group_id
}

output "cluster_primary_security_group_id" {
  description = "EKS 叢集主要安全群組 ID"
  value       = module.eks.cluster_primary_security_group_id
}

output "node_role_arns" {
  description = "EKS 節點群組的 IAM 角色 ARN"
  value       = [for ng in module.eks.eks_managed_node_groups : ng.iam_role_arn]
}
