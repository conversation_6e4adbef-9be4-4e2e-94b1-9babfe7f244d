variable "cluster_name" {
  description = "EKS 叢集名稱"
  type        = string

  validation {
    condition     = can(regex("^[a-zA-Z][a-zA-Z0-9-]*[a-zA-Z0-9]$", var.cluster_name))
    error_message = "叢集名稱必須以字母開頭，以字母或數字結尾，只能包含字母、數字和連字號。"
  }

  validation {
    condition     = length(var.cluster_name) >= 1 && length(var.cluster_name) <= 100
    error_message = "叢集名稱長度必須在 1 到 100 個字元之間。"
  }
}

variable "cluster_version" {
  description = "Kubernetes 版本"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID"
  type        = string

  validation {
    condition     = can(regex("^vpc-[a-z0-9]{8,17}$", var.vpc_id))
    error_message = "VPC ID 必須是有效的格式，例如：vpc-12345678。"
  }
}

variable "private_subnet_ids" {
  description = "私有子網 ID 列表"
  type        = list(string)

  validation {
    condition = alltrue([
      for subnet_id in var.private_subnet_ids : can(regex("^subnet-[a-z0-9]{8,17}$", subnet_id))
    ])
    error_message = "所有子網 ID 必須是有效的格式，例如：subnet-12345678。"
  }

  validation {
    condition     = length(var.private_subnet_ids) >= 2
    error_message = "至少需要 2 個私有子網以確保高可用性。"
  }
}

variable "node_groups" {
  description = "EKS 節點群組配置"
  type        = any
}

variable "tags" {
  description = "資源標籤"
  type        = map(string)
  default     = {}
}

variable "vault_kms_unseal_policy_arn" {
  description = "Vault KMS 自動解封 IAM 政策 ARN"
  type        = string
  default     = ""
}

variable "allowed_cidr_blocks" {
  description = "允許存取的 CIDR 區塊列表"
  type        = list(string)
  default     = ["**********/16"]
}

variable "additional_iam_policies" {
  description = "附加到節點群組的額外 IAM 政策 ARN 列表"
  type        = list(string)
  default = [
    "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy",
    "arn:aws:iam::aws:policy/AmazonKMSFullAccess"
  ]
}

variable "github_actions_role_arn" {
  description = "GitHub Actions IAM 角色 ARN，用於 EKS 叢集存取"
  type        = string
  default     = ""
}
