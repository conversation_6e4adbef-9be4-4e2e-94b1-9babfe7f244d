# 使用官方 EKS 模組
module "eks" {
  source  = "terraform-aws-modules/eks/aws"
  version = "~> 20.0"

  # 叢集配置
  cluster_name    = var.cluster_name
  cluster_version = var.cluster_version

  # VPC 配置
  vpc_id     = var.vpc_id
  subnet_ids = var.private_subnet_ids

  # Cluster 網路設定
  cluster_endpoint_private_access = true
  cluster_endpoint_public_access  = true

  # 預設 IAM 設定
  cluster_ip_family = "ipv4"
  create_iam_role   = true

  # 啟用 OIDC Provider
  enable_irsa = true

  # 設定安全組
  create_cluster_security_group = true
  create_node_security_group    = true

  # 節點群組設定
  eks_managed_node_groups = {
    for name, config in var.node_groups : name => {
      name           = name
      instance_types = config.instance_types
      capacity_type  = "SPOT"

      min_size     = config.min_capacity
      max_size     = config.max_capacity
      desired_size = config.desired_capacity

      # 使用 Bottlerocket ARM64
      ami_type            = "BOTTLEROCKET_ARM_64"
      platform            = "bottlerocket"
      ami_release_version = null

      # 為節點添加 EBS 磁碟區與 KMS 金鑰管理權限
      iam_role_attach_cni_policy = true

      # 附加額外的 IAM 政策
      iam_role_additional_policies = {
        for idx, policy_arn in var.additional_iam_policies :
        "policy_${idx}" => policy_arn
      }

      # 自定義標簽，移除保留前綴
      labels = {
        "cluster-autoscaler-enabled" = "true"
        "cluster-autoscaler-name"    = var.cluster_name
      }

      # 附加標籤
      tags = var.tags
    }
  }

  # 附加 Vault KMS 解封策略 (如果提供)
  node_security_group_additional_rules = {
    ingress_vpc = {
      description = "Inbound traffic from allowed CIDR blocks"
      protocol    = "-1"
      from_port   = 0
      to_port     = 0
      type        = "ingress"
      cidr_blocks = var.allowed_cidr_blocks
    }

    egress_all = {
      description = "Allow all outbound traffic"
      protocol    = "-1"
      from_port   = 0
      to_port     = 0
      type        = "egress"
      cidr_blocks = ["0.0.0.0/0"]
    }
  }

  # 啟用 CloudWatch 日誌
  cluster_enabled_log_types = ["api", "audit", "authenticator", "controllerManager", "scheduler"]

  # 標籤
  tags = var.tags

  # 自動授予叢集創建者管理員權限
  enable_cluster_creator_admin_permissions = true

  # EKS Access Entries - 授權 IAM 角色存取 Kubernetes API
  access_entries = merge(
    # GitHub Actions 角色存取權限（如果提供）
    var.github_actions_role_arn != "" ? {
      github_actions = {
        principal_arn = var.github_actions_role_arn
        type          = "STANDARD"
        policy_associations = {
          cluster_admin = {
            policy_arn = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSClusterAdminPolicy"
            access_scope = {
              type = "cluster"
            }
          }
        }
      }
    } : {}
  )

  # 使用官方 EKS 附加元件
  cluster_addons = {
    # 核心 DNS 服務
    coredns = {
      most_recent = true
    }

    # VPC CNI 網路插件
    vpc-cni = {
      most_recent = true
    }

    # kube-proxy
    kube-proxy = {
      most_recent = true
    }

    # EBS CSI 驅動程式
    aws-ebs-csi-driver = {
      most_recent = true
    }
  }
}

# Attach Vault KMS unseal policy to worker node IAM role if provided
# 暫時註解掉以避免條件計數問題 - 可在主要部署後手動附加
# locals {
#   has_vault_kms_policy = var.vault_kms_unseal_policy_arn != ""
#   first_node_group_key = element(keys(var.node_groups), 0)
# }
# 
# resource "aws_iam_role_policy_attachment" "node_group_vault_kms_unseal" {
#   count      = local.has_vault_kms_policy ? 1 : 0
#   policy_arn = var.vault_kms_unseal_policy_arn
#   role       = module.eks.eks_managed_node_groups[local.first_node_group_key].iam_role_name
# }
