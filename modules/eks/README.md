# EKS 模組

## 概覽

此模組使用官方的 `terraform-aws-modules/eks/aws` 模組來創建和管理 Amazon EKS (Elastic Kubernetes Service) 叢集。它提供了一個完整的 Kubernetes 控制平面和工作節點，支援多節點群組、IRSA (IAM Roles for Service Accounts) 以及各種 EKS 附加元件。

## 功能特性

- ✅ **託管 Kubernetes 控制平面**：完全託管的 Kubernetes API 伺服器
- ✅ **多節點群組支援**：支援多個工作節點群組配置
- ✅ **IRSA 整合**：IAM Roles for Service Accounts 支援
- ✅ **Bottlerocket OS**：使用 AWS 優化的容器作業系統
- ✅ **Spot 實例支援**：成本優化的 Spot 實例配置
- ✅ **自動縮放**：支援叢集自動縮放功能
- ✅ **CloudWatch 日誌**：完整的控制平面日誌記錄
- ✅ **EKS 附加元件**：預安裝核心附加元件
- ✅ **安全群組管理**：自動化安全群組配置
- ✅ **標籤管理**：完整的資源標籤支援

## 架構圖

```mermaid
graph TB
    subgraph "AWS Account"
        subgraph "VPC"
            subgraph "私有子網路"
                subgraph "EKS 叢集"
                    CP[控制平面<br/>託管服務]
                    
                    subgraph "節點群組"
                        WN1[工作節點 1<br/>Bottlerocket ARM64]
                        WN2[工作節點 2<br/>Bottlerocket ARM64]
                        WN3[工作節點 3<br/>Bottlerocket ARM64]
                    end
                    
                    subgraph "附加元件"
                        CNI[VPC CNI]
                        DNS[CoreDNS]
                        PROXY[kube-proxy]
                        EBS[EBS CSI Driver]
                    end
                end
            end
            
            subgraph "公有子網路"
                LB[Load Balancer]
            end
        end
        
        subgraph "IAM"
            CR[叢集角色]
            NR[節點角色]
            IRSA[IRSA 角色]
        end
        
        subgraph "CloudWatch"
            LOGS[控制平面日誌]
        end
        
        subgraph "KMS"
            KEY[加密金鑰]
        end
    end
    
    Users[使用者] --> LB
    LB --> WN1
    LB --> WN2
    LB --> WN3
    
    CP -.-> LOGS
    CP -.-> KEY
    CP --> WN1
    CP --> WN2
    CP --> WN3
    
    WN1 -.-> CR
    WN2 -.-> NR
    WN3 -.-> IRSA
```

## 使用方式

### 基本使用

```hcl
module "eks" {
  source = "./modules/eks"

  cluster_name       = "my-eks-cluster"
  cluster_version    = "1.33"
  vpc_id             = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnets

  # 基本節點群組配置
  node_groups = {
    default_node_group = {
      instance_types    = ["t4g.medium"]
      min_capacity     = 1
      max_capacity     = 3
      desired_capacity = 2
    }
  }

  # 基本安全配置
  allowed_cidr_blocks = ["172.11.0.0/16"]
  
  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### 進階配置

```hcl
module "eks" {
  source = "./modules/eks"

  cluster_name       = "production-eks"
  cluster_version    = "1.33"
  vpc_id             = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnets

  # 多節點群組配置
  node_groups = {
    # 通用工作負載節點群組
    general = {
      instance_types    = ["t4g.large", "t4g.xlarge"]
      min_capacity     = 2
      max_capacity     = 10
      desired_capacity = 3
    }
    
    # 高記憶體工作負載節點群組
    memory_optimized = {
      instance_types    = ["r6g.large", "r6g.xlarge"]
      min_capacity     = 0
      max_capacity     = 5
      desired_capacity = 1
    }
  }

  # Vault KMS 整合
  vault_kms_unseal_policy_arn = module.kms.kms_unseal_policy_arn
  
  # 額外的 IAM 政策
  additional_iam_policies = [
    "arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess"
  ]

  # 安全配置
  allowed_cidr_blocks = ["10.0.0.0/8", "172.16.0.0/12"]
  
  tags = {
    Environment        = "prod"
    Project           = "production-app"
    CostCenter        = "engineering"
    DataClassification = "confidential"
  }
}
```

## 輸入變數

| 名稱 | 描述 | 類型 | 預設值 | 必要 |
|------|------|------|--------|------|
| `cluster_name` | EKS 叢集名稱 | `string` | - | ✅ |
| `cluster_version` | EKS 叢集版本 | `string` | - | ✅ |
| `vpc_id` | VPC ID | `string` | - | ✅ |
| `private_subnet_ids` | 私有子網路 ID 列表 | `list(string)` | - | ✅ |
| `node_groups` | 節點群組配置 | `map(object)` | - | ✅ |
| `allowed_cidr_blocks` | 允許訪問的 CIDR 區塊 | `list(string)` | `[]` | ❌ |
| `vault_kms_unseal_policy_arn` | Vault KMS 解封政策 ARN | `string` | `""` | ❌ |
| `additional_iam_policies` | 額外的 IAM 政策 ARN 列表 | `list(string)` | `[]` | ❌ |
| `tags` | 資源標籤 | `map(string)` | `{}` | ❌ |

### 節點群組配置結構

```hcl
node_groups = {
  group_name = {
    instance_types    = ["t4g.medium"]  # EC2 實例類型列表
    min_capacity     = 1                # 最小節點數
    max_capacity     = 5                # 最大節點數  
    desired_capacity = 2                # 期望節點數
  }
}
```

## 輸出值

| 名稱 | 描述 |
|------|------|
| `cluster_id` | EKS 叢集 ID |
| `cluster_arn` | EKS 叢集 ARN |
| `cluster_endpoint` | EKS 叢集 API 端點 |
| `cluster_version` | EKS 叢集版本 |
| `cluster_security_group_id` | 叢集安全群組 ID |
| `cluster_iam_role_arn` | 叢集 IAM 角色 ARN |
| `cluster_certificate_authority_data` | 叢集 CA 憑證資料 |
| `cluster_oidc_issuer_url` | OIDC 發行者 URL |
| `oidc_provider_arn` | OIDC 提供者 ARN |
| `node_security_group_id` | 節點安全群組 ID |

## EKS 附加元件

此模組自動安裝以下核心附加元件：

### 核心附加元件
- **CoreDNS**：叢集 DNS 服務
- **VPC CNI**：AWS VPC 網路插件
- **kube-proxy**：Kubernetes 網路代理
- **EBS CSI Driver**：Amazon EBS 儲存驅動程式

### 配置範例
```hcl
cluster_addons = {
  coredns = {
    most_recent = true
  }
  vpc-cni = {
    most_recent = true
  }
  kube-proxy = {
    most_recent = true
  }
  aws-ebs-csi-driver = {
    most_recent = true
  }
}
```

## 最佳實踐

### 1. 節點群組設計
```hcl
# 建議的節點群組配置
node_groups = {
  # 系統工作負載
  system = {
    instance_types    = ["t4g.medium"]
    min_capacity     = 2
    max_capacity     = 4
    desired_capacity = 2
  }
  
  # 應用程式工作負載
  application = {
    instance_types    = ["t4g.large", "t4g.xlarge"]
    min_capacity     = 1
    max_capacity     = 10
    desired_capacity = 3
  }
}
```

### 2. 安全性配置
- 使用私有子網路部署工作節點
- 限制 API 伺服器訪問的 CIDR 範圍
- 啟用 CloudWatch 日誌記錄
- 使用 IRSA 進行細粒度權限控制

### 3. 成本優化
- 使用 Spot 實例（已預設啟用）
- 使用 ARM64 實例類型（Graviton2/3）
- 配置適當的自動縮放策略
- 定期檢查和調整節點群組大小

### 4. 高可用性
- 在多個可用區域部署節點
- 設置適當的最小節點數
- 使用多種實例類型提高可用性

## 故障排除

### 常見問題

#### 1. 叢集創建失敗
**問題**：EKS 叢集創建超時或失敗
```
Error: error creating EKS Cluster: timeout while waiting for state to become 'ACTIVE'
```

**解決方案**：
- 檢查 IAM 角色權限
- 確認子網路配置正確
- 檢查安全群組規則
- 驗證 VPC 設定

#### 2. 節點無法加入叢集
**問題**：工作節點無法加入 EKS 叢集

**解決方案**：
```bash
# 檢查節點群組狀態
aws eks describe-nodegroup --cluster-name <cluster-name> --nodegroup-name <nodegroup-name>

# 檢查節點實例狀態
aws ec2 describe-instances --filters "Name=tag:eks:cluster-name,Values=<cluster-name>"
```

#### 3. IRSA 權限問題
**問題**：Service Account 無法承擔 IAM 角色

**解決方案**：
- 檢查 OIDC 提供者配置
- 驗證 IAM 角色信任政策
- 確認 Service Account 註解

### 調試指令

```bash
# 檢查叢集狀態
kubectl cluster-info
kubectl get nodes

# 檢查系統 Pod
kubectl get pods -n kube-system

# 檢查 EKS 附加元件
aws eks list-addons --cluster-name <cluster-name>

# 檢查節點群組
aws eks describe-nodegroup --cluster-name <cluster-name> --nodegroup-name <nodegroup-name>
```

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0
- **Kubernetes Provider**: ~> 2.20
- **terraform-aws-modules/eks/aws**: ~> 20.0

## 相關資源

- [Amazon EKS 用戶指南](https://docs.aws.amazon.com/eks/latest/userguide/)
- [terraform-aws-modules/eks/aws](https://registry.terraform.io/modules/terraform-aws-modules/eks/aws/latest)
- [EKS 最佳實踐指南](https://aws.github.io/aws-eks-best-practices/)
- [Kubernetes 文檔](https://kubernetes.io/docs/)

## 範例

查看 [examples](../../examples/) 目錄以獲取更多使用範例。
