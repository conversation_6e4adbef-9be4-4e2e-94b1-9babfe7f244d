# RDS 模組

## 概覽

此模組使用官方的 `terraform-aws-modules/rds-aurora/aws` 模組來創建和管理 Amazon Aurora PostgreSQL 叢集。它提供了一個高可用性、可擴展的關聯式資料庫解決方案，支援自動備份、監控和安全性功能。

## 功能特性

- ✅ **Aurora PostgreSQL**：使用 AWS Aurora PostgreSQL 引擎
- ✅ **高可用性**：多可用區域部署支援
- ✅ **自動備份**：可配置的備份保留期
- ✅ **加密支援**：靜態和傳輸中加密
- ✅ **監控整合**：CloudWatch 和 Performance Insights
- ✅ **安全群組**：自動化網路安全配置
- ✅ **參數群組**：可自訂的資料庫參數
- ✅ **子網路群組**：私有子網路部署
- ✅ **標籤管理**：完整的資源標籤支援
- ✅ **密碼管理**：與 AWS Secrets Manager 整合

## 架構圖

```mermaid
graph TB
    subgraph "VPC"
        subgraph "私有子網路 A"
            RDS1[Aurora 實例 1<br/>Primary]
        end
        
        subgraph "私有子網路 B"
            RDS2[Aurora 實例 2<br/>Reader]
        end
        
        subgraph "私有子網路 C"
            RDS3[Aurora 實例 3<br/>Reader]
        end
        
        subgraph "安全群組"
            SG[RDS 安全群組<br/>Port 5432]
        end
        
        subgraph "子網路群組"
            SUBNET[DB 子網路群組]
        end
    end
    
    subgraph "AWS 服務"
        subgraph "Secrets Manager"
            SECRET[資料庫密碼]
        end
        
        subgraph "CloudWatch"
            LOGS[資料庫日誌]
            METRICS[效能指標]
        end
        
        subgraph "KMS"
            KEY[加密金鑰]
        end
    end
    
    subgraph "應用程式"
        APP1[應用程式 1]
        APP2[應用程式 2]
    end
    
    APP1 --> SG
    APP2 --> SG
    SG --> RDS1
    SG --> RDS2
    SG --> RDS3
    
    RDS1 -.-> SECRET
    RDS1 -.-> LOGS
    RDS1 -.-> METRICS
    RDS1 -.-> KEY
    
    RDS1 --> RDS2
    RDS1 --> RDS3
    
    SUBNET --> RDS1
    SUBNET --> RDS2
    SUBNET --> RDS3
```

## 使用方式

### 基本使用

```hcl
module "rds" {
  source = "./modules/rds"

  identifier     = "my-database"
  engine_version = "15.4"
  instance_class = "db.t4g.medium"
  
  # 網路配置
  vpc_id         = module.vpc.vpc_id
  subnet_ids     = module.vpc.private_subnets
  
  # 資料庫配置
  database_name = "myapp"
  username      = "postgres"
  
  # 安全配置
  allowed_cidr_blocks     = ["172.11.0.0/16"]
  allowed_security_groups = [module.eks.node_security_group_id]
  
  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### 生產環境配置

```hcl
module "rds" {
  source = "./modules/rds"

  identifier     = "production-db"
  engine_version = "15.4"
  instance_class = "db.r6g.xlarge"
  
  # 高可用性配置
  instances = {
    primary = {}
    reader1 = {}
    reader2 = {}
  }
  
  # 網路配置
  vpc_id         = module.vpc.vpc_id
  subnet_ids     = module.vpc.private_subnets
  
  # 資料庫配置
  database_name = "production_app"
  username      = "app_user"
  
  # 備份配置
  backup_retention_period = 30
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  # 效能配置
  performance_insights_enabled = true
  monitoring_interval         = 60
  
  # 安全配置
  storage_encrypted           = true
  allowed_cidr_blocks        = ["10.0.0.0/8"]
  allowed_security_groups    = [
    module.eks.node_security_group_id,
    module.app.security_group_id
  ]
  
  tags = {
    Environment        = "prod"
    Project           = "production-app"
    CostCenter        = "engineering"
    DataClassification = "confidential"
    BackupRequired     = "true"
  }
}
```

## 輸入變數

| 名稱 | 描述 | 類型 | 預設值 | 必要 |
|------|------|------|--------|------|
| `identifier` | RDS 叢集識別碼 | `string` | - | ✅ |
| `engine_version` | PostgreSQL 引擎版本 | `string` | - | ✅ |
| `instance_class` | RDS 實例類型 | `string` | - | ✅ |
| `vpc_id` | VPC ID | `string` | - | ✅ |
| `subnet_ids` | 子網路 ID 列表 | `list(string)` | - | ✅ |
| `database_name` | 資料庫名稱 | `string` | - | ✅ |
| `username` | 主要使用者名稱 | `string` | - | ✅ |
| `instances` | Aurora 實例配置 | `map(object)` | `{ primary = {} }` | ❌ |
| `allowed_cidr_blocks` | 允許訪問的 CIDR 區塊 | `list(string)` | `[]` | ❌ |
| `allowed_security_groups` | 允許訪問的安全群組 | `list(string)` | `[]` | ❌ |
| `backup_retention_period` | 備份保留天數 | `number` | `7` | ❌ |
| `backup_window` | 備份時間窗口 | `string` | `"03:00-04:00"` | ❌ |
| `maintenance_window` | 維護時間窗口 | `string` | `"sun:04:00-sun:05:00"` | ❌ |
| `storage_encrypted` | 是否啟用儲存加密 | `bool` | `true` | ❌ |
| `performance_insights_enabled` | 是否啟用 Performance Insights | `bool` | `false` | ❌ |
| `monitoring_interval` | 監控間隔（秒） | `number` | `0` | ❌ |
| `tags` | 資源標籤 | `map(string)` | `{}` | ❌ |

## 輸出值

| 名稱 | 描述 |
|------|------|
| `cluster_id` | Aurora 叢集識別碼 |
| `cluster_arn` | Aurora 叢集 ARN |
| `cluster_endpoint` | 寫入端點 |
| `cluster_reader_endpoint` | 讀取端點 |
| `cluster_port` | 資料庫連接埠 |
| `cluster_database_name` | 資料庫名稱 |
| `cluster_master_username` | 主要使用者名稱 |
| `security_group_id` | 資料庫安全群組 ID |
| `subnet_group_name` | 資料庫子網路群組名稱 |

## 實例類型建議

### 開發環境
```hcl
instance_class = "db.t4g.medium"  # 2 vCPU, 4 GB RAM
# 或
instance_class = "db.t4g.large"   # 2 vCPU, 8 GB RAM
```

### 測試環境
```hcl
instance_class = "db.r6g.large"   # 2 vCPU, 16 GB RAM
# 或
instance_class = "db.r6g.xlarge"  # 4 vCPU, 32 GB RAM
```

### 生產環境
```hcl
instance_class = "db.r6g.2xlarge"  # 8 vCPU, 64 GB RAM
# 或
instance_class = "db.r6g.4xlarge"  # 16 vCPU, 128 GB RAM
```

## 最佳實踐

### 1. 高可用性配置
```hcl
# 多實例配置
instances = {
  primary = {
    instance_class = "db.r6g.xlarge"
  }
  reader1 = {
    instance_class = "db.r6g.large"
  }
  reader2 = {
    instance_class = "db.r6g.large"
  }
}
```

### 2. 備份策略
```hcl
backup_retention_period = 30              # 30 天備份保留
backup_window          = "03:00-04:00"    # 低峰時段備份
maintenance_window     = "sun:04:00-sun:05:00"  # 週日維護
```

### 3. 安全性配置
- 使用私有子網路部署
- 啟用儲存加密
- 限制安全群組訪問
- 使用 Secrets Manager 管理密碼

### 4. 效能優化
- 啟用 Performance Insights
- 配置適當的監控間隔
- 使用讀取副本分散讀取負載
- 選擇適當的實例類型

## 故障排除

### 常見問題

#### 1. 連接超時
**問題**：應用程式無法連接到資料庫
```
Error: timeout connecting to database
```

**解決方案**：
- 檢查安全群組規則
- 確認子網路路由配置
- 驗證 DNS 解析

#### 2. 效能問題
**問題**：資料庫查詢緩慢

**解決方案**：
```sql
-- 檢查慢查詢
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 檢查連接數
SELECT count(*) FROM pg_stat_activity;
```

#### 3. 儲存空間不足
**問題**：資料庫儲存空間即將用盡

**解決方案**：
- Aurora 會自動擴展儲存空間
- 檢查不必要的資料和日誌
- 考慮資料歸檔策略

### 監控指標

```bash
# 檢查叢集狀態
aws rds describe-db-clusters --db-cluster-identifier <cluster-id>

# 檢查實例狀態
aws rds describe-db-instances --db-instance-identifier <instance-id>

# 檢查效能指標
aws cloudwatch get-metric-statistics \
  --namespace AWS/RDS \
  --metric-name CPUUtilization \
  --dimensions Name=DBClusterIdentifier,Value=<cluster-id>
```

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0
- **terraform-aws-modules/rds-aurora/aws**: ~> 8.0

## 相關資源

- [Amazon Aurora 用戶指南](https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/)
- [terraform-aws-modules/rds-aurora/aws](https://registry.terraform.io/modules/terraform-aws-modules/rds-aurora/aws/latest)
- [PostgreSQL 文檔](https://www.postgresql.org/docs/)
- [Aurora PostgreSQL 最佳實踐](https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/AuroraPostgreSQL.BestPractices.html)

## 範例

查看 [examples](../../examples/) 目錄以獲取更多使用範例。
