variable "identifier" {
  description = "RDS 執行個體識別碼"
  type        = string

  validation {
    condition     = can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.identifier))
    error_message = "RDS 識別碼必須以小寫字母開頭，以字母或數字結尾，只能包含小寫字母、數字和連字號。"
  }

  validation {
    condition     = length(var.identifier) >= 1 && length(var.identifier) <= 63
    error_message = "RDS 識別碼長度必須在 1 到 63 個字元之間。"
  }
}

variable "instance_class" {
  description = "RDS 執行個體類型"
  type        = string
  default     = "db.t4g.medium"
}

variable "allocated_storage" {
  description = "分配的儲存空間大小 (GB)"
  type        = number
  default     = 20

  validation {
    condition     = var.allocated_storage >= 20 && var.allocated_storage <= 65536
    error_message = "分配的儲存空間必須在 20GB 到 65536GB 之間。"
  }
}

variable "max_allocated_storage" {
  description = "自動擴展的最大儲存空間大小 (GB)"
  type        = number
  default     = 100
}

variable "storage_type" {
  description = "儲存類型"
  type        = string
  default     = "gp3"
}

variable "storage_encrypted" {
  description = "是否加密儲存"
  type        = bool
  default     = true
}

variable "engine_version" {
  description = "Aurora PostgreSQL 引擎版本"
  type        = string
  default     = "16.6"
}

variable "username" {
  description = "資料庫管理員使用者名稱"
  type        = string
  default     = "postgres"
}

variable "password" {
  description = "資料庫管理員密碼"
  type        = string
  sensitive   = true
}

variable "db_name" {
  description = "資料庫名稱"
  type        = string
}

variable "port" {
  description = "資料庫連接埠"
  type        = number
  default     = 5432
}

variable "publicly_accessible" {
  description = "是否可公開存取"
  type        = bool
  default     = true
}

variable "subnet_ids" {
  description = "子網路 ID 列表"
  type        = list(string)
}

variable "vpc_id" {
  description = "VPC ID"
  type        = string
}

variable "allowed_security_groups" {
  description = "允許連接的安全群組 ID 列表"
  type        = list(string)
  default     = []
}

variable "allowed_cidr_blocks" {
  description = "允許連接的 CIDR 區塊列表"
  type        = list(string)
  default     = ["**********/16"]
}

variable "multi_az" {
  description = "是否啟用多可用區"
  type        = bool
  default     = true
}

variable "backup_retention_period" {
  description = "備份保留天數"
  type        = number
  default     = 7
}

variable "backup_window" {
  description = "備份時間窗口"
  type        = string
  default     = "03:00-04:00"
}

variable "maintenance_window" {
  description = "維護時間窗口"
  type        = string
  default     = "mon:04:00-mon:05:00"
}

variable "skip_final_snapshot" {
  description = "刪除叢集前是否略過最終快照"
  type        = bool
  default     = true
}

variable "deletion_protection" {
  description = "資料庫叢集是否啟用刪除保護"
  type        = bool
  default     = false
}

variable "apply_immediately" {
  description = "是否立即套用變更"
  type        = bool
  default     = false
}

variable "tags" {
  description = "資源標籤"
  type        = map(string)
  default     = {}
}
