output "db_cluster_id" {
  description = "Aurora 叢集 ID"
  value       = module.aurora.cluster_id
}

output "db_cluster_endpoint" {
  description = "Aurora 叢集寫入端點"
  value       = module.aurora.cluster_endpoint
}

output "db_cluster_reader_endpoint" {
  description = "Aurora 叢集讀取端點"
  value       = module.aurora.cluster_reader_endpoint
}

output "db_instance_id" {
  description = "Aurora 實例 ID"
  value       = module.aurora.cluster_instances["1"].id
}

output "db_instance_endpoint" {
  description = "Aurora 實例端點"
  value       = module.aurora.cluster_instances["1"].endpoint
}

output "db_instance_name" {
  description = "Aurora 資料庫名稱"
  value       = module.aurora.cluster_database_name
}

output "db_instance_username" {
  description = "Aurora 管理員使用者名稱"
  value       = module.aurora.cluster_master_username
}

output "db_instance_port" {
  description = "Aurora 端口"
  value       = module.aurora.cluster_port
}

output "db_security_group_id" {
  description = "Aurora 安全群組 ID"
  value       = module.aurora.security_group_id
}

output "db_subnet_group_id" {
  description = "Aurora 子網路群組 ID"
  value       = module.aurora.db_subnet_group_name
}

output "db_parameter_group_id" {
  description = "Aurora 參數群組 ID"
  value       = module.aurora.db_cluster_parameter_group_id
}
