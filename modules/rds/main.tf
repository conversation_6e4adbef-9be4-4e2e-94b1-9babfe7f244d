# 使用官方 RDS Aurora 模組
module "aurora" {
  source  = "terraform-aws-modules/rds-aurora/aws"
  version = "~> 9.0"

  name = var.identifier

  # 資料庫引擎配置
  engine         = "aurora-postgresql"
  engine_version = var.engine_version
  instance_class = var.instance_class
  instances = {
    # 建立一個主要實例
    1 = {
      identifier          = "${var.identifier}-instance-1"
      publicly_accessible = var.publicly_accessible
      instance_class      = var.instance_class
    }
  }

  # 資料庫認證
  database_name               = var.db_name
  master_username             = var.username
  master_password             = var.password
  manage_master_user_password = false

  # 網路配置
  vpc_id                 = var.vpc_id
  subnets                = var.subnet_ids
  create_db_subnet_group = true
  create_security_group  = true
  security_group_rules = {
    vpc_ingress = {
      description = "Allow database access from allowed CIDR blocks"
      cidr_blocks = var.allowed_cidr_blocks
    }
  }
  vpc_security_group_ids = var.allowed_security_groups

  # 參數群組與叢集參數
  create_db_parameter_group         = false
  create_db_cluster_parameter_group = true
  db_cluster_parameter_group_name   = "${var.identifier}-pg"
  db_cluster_parameter_group_family = "aurora-postgresql16"
  db_cluster_parameter_group_parameters = [
    {
      name  = "log_connections"
      value = "1"
    }
  ]

  # 儲存配置
  storage_encrypted = var.storage_encrypted

  # 維護配置
  backup_retention_period      = var.backup_retention_period
  preferred_backup_window      = var.backup_window
  preferred_maintenance_window = var.maintenance_window
  skip_final_snapshot          = var.skip_final_snapshot
  deletion_protection          = var.deletion_protection
  apply_immediately            = var.apply_immediately

  # 標籤
  tags = var.tags
}
