variable "resource_prefix" {
  description = "標準化資源名稱前綴"
  type        = string
}

variable "region" {
  description = "AWS 區域"
  type        = string
  default     = "ap-east-2"
}

variable "vpc_cidr" {
  description = "VPC CIDR 區塊"
  type        = string
}

variable "availability_zones" {
  description = "可用區域列表"
  type        = list(string)
}

variable "private_subnets" {
  description = "私有子網路 CIDR 區塊列表"
  type        = list(string)
}

variable "public_subnets" {
  description = "公有子網路 CIDR 區塊列表"
  type        = list(string)
}

variable "tags" {
  description = "資源標籤"
  type        = map(string)
  default     = {}
}

variable "enable_vpc_endpoints" {
  description = "是否啟用 VPC 端點"
  type        = bool
  default     = true
}

# 移除重複的 name_prefix 變數，統一使用 resource_prefix
