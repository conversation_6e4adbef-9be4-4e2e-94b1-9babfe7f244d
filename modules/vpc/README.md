# VPC 模組

## 概覽

此模組使用官方的 `terraform-aws-modules/vpc/aws` 模組來創建和管理 AWS VPC 基礎設施。它提供了一個完整的網路基礎，包括公有和私有子網路、NAT 閘道、Internet 閘道以及 VPC 端點。

## 功能特性

- ✅ **多可用區域支援**：自動在多個可用區域創建子網路
- ✅ **公有/私有子網路**：支援公有和私有子網路配置
- ✅ **NAT 閘道**：提供私有子網路的外網訪問
- ✅ **VPC 端點**：支援 AWS 服務的私有連接
- ✅ **DNS 支援**：啟用 DNS 主機名和解析
- ✅ **成本優化**：使用單一 NAT 閘道降低成本
- ✅ **安全群組**：為 VPC 端點提供安全群組
- ✅ **標籤管理**：完整的資源標籤支援

## 架構圖

```mermaid
graph TB
    subgraph "VPC (**********/16)"
        subgraph "可用區域 A"
            PubA[公有子網路<br/>**********/24]
            PrivA[私有子網路<br/>***********/24]
        end
        
        subgraph "可用區域 B"
            PubB[公有子網路<br/>**********/24]
            PrivB[私有子網路<br/>***********/24]
        end
        
        subgraph "可用區域 C"
            PubC[公有子網路<br/>**********/24]
            PrivC[私有子網路<br/>***********/24]
        end
        
        IGW[Internet Gateway]
        NAT[NAT Gateway]
        
        subgraph "VPC 端點"
            EP1[EC2 端點]
            EP2[ELB 端點]
            EP3[STS 端點]
        end
    end
    
    Internet((Internet))
    
    Internet --> IGW
    IGW --> PubA
    IGW --> PubB
    IGW --> PubC
    
    PubA --> NAT
    NAT --> PrivA
    NAT --> PrivB
    NAT --> PrivC
    
    PrivA -.-> EP1
    PrivB -.-> EP2
    PrivC -.-> EP3
```

## 使用方式

### 基本使用

```hcl
module "vpc" {
  source = "./modules/vpc"

  resource_prefix    = "my-project"
  region            = "ap-east-2"
  vpc_cidr          = "**********/16"
  availability_zones = ["ap-east-2a", "ap-east-2b", "ap-east-2c"]
  
  private_subnets = ["***********/24", "***********/24", "***********/24"]
  public_subnets  = ["**********/24", "**********/24", "**********/24"]
  
  enable_vpc_endpoints = true
  
  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### 進階配置

```hcl
module "vpc" {
  source = "./modules/vpc"

  resource_prefix    = "production-app"
  region            = "ap-east-2"
  vpc_cidr          = "10.0.0.0/16"
  availability_zones = ["ap-east-2a", "ap-east-2b", "ap-east-2c"]
  
  # 更大的子網路用於生產環境
  private_subnets = ["10.0.1.0/24", "10.0.2.0/24", "10.0.3.0/24"]
  public_subnets  = ["10.0.101.0/24", "10.0.102.0/24", "10.0.103.0/24"]
  
  # 啟用 VPC 端點以提高安全性和性能
  enable_vpc_endpoints = true
  
  tags = {
    Environment        = "prod"
    Project           = "production-app"
    CostCenter        = "engineering"
    DataClassification = "confidential"
  }
}
```

## 輸入變數

| 名稱 | 描述 | 類型 | 預設值 | 必要 |
|------|------|------|--------|------|
| `resource_prefix` | 標準化資源名稱前綴 | `string` | - | ✅ |
| `region` | AWS 區域 | `string` | `"ap-east-2"` | ❌ |
| `vpc_cidr` | VPC CIDR 區塊 | `string` | - | ✅ |
| `availability_zones` | 可用區域列表 | `list(string)` | - | ✅ |
| `private_subnets` | 私有子網路 CIDR 區塊列表 | `list(string)` | - | ✅ |
| `public_subnets` | 公有子網路 CIDR 區塊列表 | `list(string)` | - | ✅ |
| `tags` | 資源標籤 | `map(string)` | `{}` | ❌ |
| `enable_vpc_endpoints` | 是否啟用 VPC 端點 | `bool` | `true` | ❌ |

## 輸出值

| 名稱 | 描述 |
|------|------|
| `vpc_id` | VPC 的 ID |
| `vpc_cidr_block` | VPC 的 CIDR 區塊 |
| `private_subnets` | 私有子網路的 ID 列表 |
| `public_subnets` | 公有子網路的 ID 列表 |
| `nat_public_ips` | NAT 閘道的公有 IP 列表 |
| `internet_gateway_id` | Internet Gateway 的 ID |
| `nat_gateway_ids` | NAT Gateway 的 ID 列表 |
| `vpc_endpoints_security_group_id` | VPC 端點安全群組的 ID |

## VPC 端點

此模組自動創建以下 VPC 端點（當 `enable_vpc_endpoints = true` 時）：

- **EC2 端點**：用於 EC2 API 調用
- **Elastic Load Balancing 端點**：用於 ELB API 調用  
- **STS 端點**：用於 AWS STS API 調用

這些端點允許私有子網路中的資源安全地訪問 AWS 服務，而無需通過 Internet。

## 最佳實踐

### 1. CIDR 規劃
```hcl
# 建議的 CIDR 分配
vpc_cidr = "**********/16"  # 65,536 個 IP 地址

# 公有子網路 (每個 /24 = 256 個 IP)
public_subnets = [
  "**********/24",   # AZ-a
  "**********/24",   # AZ-b  
  "**********/24"    # AZ-c
]

# 私有子網路 (每個 /24 = 256 個 IP)
private_subnets = [
  "***********/24",  # AZ-a
  "***********/24",  # AZ-b
  "***********/24"   # AZ-c
]
```

### 2. 成本優化
- 使用單一 NAT 閘道（已預設啟用）
- 考慮使用 NAT 實例替代 NAT 閘道（適用於開發環境）
- 啟用 VPC 端點減少數據傳輸成本

### 3. 安全性
- 私有子網路用於應用程式和資料庫
- 公有子網路僅用於負載均衡器和 NAT 閘道
- 使用 VPC 端點避免流量經過 Internet

### 4. 高可用性
- 至少使用 3 個可用區域
- 確保每個可用區域都有公有和私有子網路

## 故障排除

### 常見問題

#### 1. CIDR 衝突
**問題**：VPC CIDR 與現有網路衝突
```
Error: InvalidVpc.Range: The CIDR '**********/16' conflicts with another subnet
```

**解決方案**：
- 檢查現有 VPC 的 CIDR 範圍
- 選擇不衝突的 CIDR 區塊
- 使用 AWS VPC CIDR 計算器規劃網路

#### 2. 可用區域不存在
**問題**：指定的可用區域在區域中不存在
```
Error: InvalidAvailabilityZone.NotFound
```

**解決方案**：
```bash
# 查看可用的可用區域
aws ec2 describe-availability-zones --region ap-east-2
```

#### 3. VPC 端點連接問題
**問題**：無法通過 VPC 端點訪問 AWS 服務

**解決方案**：
- 檢查安全群組規則
- 確認路由表配置
- 驗證 DNS 解析設定

### 調試指令

```bash
# 檢查 VPC 配置
terraform output vpc_id
terraform output vpc_cidr_block

# 檢查子網路
terraform output private_subnets
terraform output public_subnets

# 檢查 NAT 閘道
terraform output nat_public_ips
```

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0
- **terraform-aws-modules/vpc/aws**: ~> 5.0

## 相關資源

- [AWS VPC 用戶指南](https://docs.aws.amazon.com/vpc/latest/userguide/)
- [terraform-aws-modules/vpc/aws](https://registry.terraform.io/modules/terraform-aws-modules/vpc/aws/latest)
- [VPC 端點文檔](https://docs.aws.amazon.com/vpc/latest/privatelink/vpc-endpoints.html)
- [CIDR 計算器](https://www.subnet-calculator.com/)

## 範例

查看 [examples](../../examples/) 目錄以獲取更多使用範例。
