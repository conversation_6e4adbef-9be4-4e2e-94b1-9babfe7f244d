output "vpc_id" {
  description = "VPC 的 ID"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "VPC 的 CIDR 區塊"
  value       = module.vpc.vpc_cidr_block
}

output "private_subnets" {
  description = "私有子網路的 ID 列表"
  value       = module.vpc.private_subnets
}

output "public_subnets" {
  description = "公有子網路的 ID 列表"
  value       = module.vpc.public_subnets
}

output "nat_public_ips" {
  description = "NAT 閘道的公有 IP 列表"
  value       = module.vpc.nat_public_ips
}

output "private_route_table_ids" {
  description = "私有路由表的 ID 列表"
  value       = module.vpc.private_route_table_ids
}

output "public_route_table_ids" {
  description = "公有路由表的 ID 列表"
  value       = module.vpc.public_route_table_ids
}

output "vpc_endpoint_ec2_id" {
  description = "EC2 VPC 端點的 ID"
  value       = var.enable_vpc_endpoints ? aws_vpc_endpoint.ec2[0].id : null
}

output "vpc_endpoint_elb_id" {
  description = "ELB VPC 端點的 ID"
  value       = var.enable_vpc_endpoints ? aws_vpc_endpoint.elasticloadbalancing[0].id : null
}

output "vpc_endpoint_sts_id" {
  description = "STS VPC 端點的 ID"
  value       = var.enable_vpc_endpoints ? aws_vpc_endpoint.sts[0].id : null
}

output "vpc_endpoints_security_group_id" {
  description = "VPC 端點安全群組的 ID"
  value       = var.enable_vpc_endpoints ? aws_security_group.vpc_endpoints[0].id : null
}
