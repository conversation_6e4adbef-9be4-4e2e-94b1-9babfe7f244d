module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "~> 5.0"

  name = "${var.resource_prefix}-vpc"
  cidr = var.vpc_cidr

  azs             = var.availability_zones
  private_subnets = var.private_subnets
  public_subnets  = var.public_subnets

  # 使用單一 NAT Gateway 以降低成本
  enable_nat_gateway     = true
  single_nat_gateway     = true
  one_nat_gateway_per_az = false

  # 啟用 DNS 支援
  enable_dns_hostnames = true
  enable_dns_support   = true

  # VPC 流日誌設定已關閉
  enable_flow_log = false
  # 以下設定在流日誌關閉時不生效
  # create_flow_log_cloudwatch_log_group = true
  # create_flow_log_cloudwatch_iam_role  = true
  # flow_log_destination_type         = "cloud-watch-logs"
  # flow_log_cloudwatch_log_group_name_prefix = "/aws/${var.project}-vpc-flow-logs/"
  # flow_log_cloudwatch_log_group_retention_in_days = 14

  # 子網路標籤設定 (支援 Kubernetes 與 AWS Load Balancer Controller)
  public_subnet_tags = {
    "kubernetes.io/role/elb" = "1"
    "Type"                   = "Public"
  }

  private_subnet_tags = {
    "kubernetes.io/role/internal-elb" = "1"
    "Type"                            = "Private"
  }

  tags = var.tags
}

# 創建 VPC 端點安全群組
resource "aws_security_group" "vpc_endpoints" {
  count       = var.enable_vpc_endpoints ? 1 : 0
  name_prefix = "${var.resource_prefix}-vpc-endpoints-sg"
  vpc_id      = module.vpc.vpc_id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [module.vpc.vpc_cidr_block]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.resource_prefix}-vpc-endpoints-sg"
    }
  )
}

# 創建 EC2 VPC 端點
resource "aws_vpc_endpoint" "ec2" {
  count               = var.enable_vpc_endpoints ? 1 : 0
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${var.region}.ec2"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true
  subnet_ids          = module.vpc.private_subnets

  security_group_ids = [
    aws_security_group.vpc_endpoints[0].id
  ]

  tags = merge(
    var.tags,
    {
      Name = "${var.resource_prefix}-ec2-endpoint"
    }
  )
}

# 創建 Elastic Load Balancing 端點
resource "aws_vpc_endpoint" "elasticloadbalancing" {
  count               = var.enable_vpc_endpoints ? 1 : 0
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${var.region}.elasticloadbalancing"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true
  subnet_ids          = module.vpc.private_subnets

  security_group_ids = [
    aws_security_group.vpc_endpoints[0].id
  ]

  tags = merge(
    var.tags,
    {
      Name = "${var.resource_prefix}-elb-endpoint"
    }
  )
}

# 創建 STS 端點（用於 IAM 角色）
resource "aws_vpc_endpoint" "sts" {
  count               = var.enable_vpc_endpoints ? 1 : 0
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${var.region}.sts"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true
  subnet_ids          = module.vpc.private_subnets

  security_group_ids = [
    aws_security_group.vpc_endpoints[0].id
  ]

  tags = merge(
    var.tags,
    {
      Name = "${var.resource_prefix}-sts-endpoint"
    }
  )
}
