output "iam_role_arn" {
  description = "External DNS 的 IAM 角色 ARN"
  value       = module.irsa_role.iam_role_arn
}

output "iam_role_name" {
  description = "External DNS 的 IAM 角色名稱"
  value       = module.irsa_role.iam_role_name
}

# IAM 角色可能已附加多個政策，但我們主要關心的是 External DNS 的政策
output "iam_policy_arn" {
  description = "External DNS 的 IAM 政策 ARN（由 IRSA 模組自動管理）"
  value       = module.irsa_role.iam_role_arn
}

output "helm_release_name" {
  description = "Helm 釋出名稱"
  value       = helm_release.external_dns.name
}

output "namespace" {
  description = "Kubernetes 命名空間"
  value       = var.namespace
}

output "service_account_name" {
  description = "服務帳號名稱"
  value       = var.service_account_name
}
