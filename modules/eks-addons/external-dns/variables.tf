variable "cluster_name" {
  description = "EKS 叢集的名稱"
  type        = string
}

variable "region" {
  description = "AWS 區域"
  type        = string
}

variable "oidc_provider_arn" {
  description = "EKS OIDC Provider ARN"
  type        = string
}

variable "namespace" {
  description = "Kubernetes 命名空間"
  type        = string
  default     = "external-dns"
}

variable "service_account_name" {
  description = "外部 DNS 的 Kubernetes 服務帳號名稱"
  type        = string
  default     = "external-dns"
}

variable "create_namespace" {
  description = "是否創建命名空間"
  type        = bool
  default     = true
}

variable "chart_version" {
  description = "External DNS Helm Chart 版本"
  type        = string
  default     = "1.13.1" # 根據需要更新到最新版本
}

variable "hosted_zone_arns" {
  description = "External DNS 可以管理的 Route53 託管區域 ARN 列表"
  type        = list(string)
  default     = ["arn:aws:route53:::hostedzone/*"]
}

variable "domain_filters" {
  description = "限制哪些域名會被同步 (e.g. 'example.com')"
  type        = list(string)
  default     = []
}

variable "policy" {
  description = "告訴 External DNS 應如何同步 DNS 記錄 (sync, upsert-only)"
  type        = string
  default     = "upsert-only" # 安全的選擇，避免刪除現有記錄
}

variable "zone_type" {
  description = "Route53 區域類型 (public, private)"
  type        = string
  default     = "public"
}

variable "txt_owner_id" {
  description = "TXT 記錄擁有者識別碼，用於識別由 External DNS 創建的記錄"
  type        = string
  default     = "" # 預設將使用叢集名稱
}

variable "txt_prefix" {
  description = "TXT 記錄前綴，用於避免 DNS 名稱衝突問題"
  type        = string
  default     = "cname-" # 預設使用 cname- 作為前綴
}

variable "tags" {
  description = "標籤地圖"
  type        = map(string)
  default     = {}
}
