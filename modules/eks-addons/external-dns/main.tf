locals {
  role_name = "${var.cluster_name}-external-dns"
}

# 使用 IRSA 模組建立 IAM 角色
module "irsa_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "~> 5.30.0"

  role_name        = local.role_name
  role_description = "IAM role for External DNS"

  attach_external_dns_policy    = true
  external_dns_hosted_zone_arns = var.hosted_zone_arns

  oidc_providers = {
    eks = {
      provider_arn               = var.oidc_provider_arn
      namespace_service_accounts = ["${var.namespace}:${var.service_account_name}"]
    }
  }

  tags = var.tags
}

# 建立命名空間（如果需要）
resource "kubernetes_namespace" "this" {
  count = var.create_namespace ? 1 : 0

  metadata {
    name = var.namespace
    labels = {
      name = var.namespace
    }
  }
}

# 安裝 External DNS
resource "helm_release" "external_dns" {
  name            = "external-dns"
  repository      = "https://kubernetes-sigs.github.io/external-dns/"
  chart           = "external-dns"
  version         = var.chart_version
  namespace       = var.namespace
  timeout         = 600  # 增加超時時間到 10 分鐘
  atomic          = true # 如果安裝失敗，自動回滾
  cleanup_on_fail = true # 安裝失敗時清理資源

  # 確保命名空間存在後再安裝
  depends_on = [kubernetes_namespace.this]

  # 使用 values.yaml 文件
  values = [
    templatefile("${path.module}/templates/external-dns-values.yaml", {
      region               = var.region
      zone_type            = var.zone_type
      service_account_name = var.service_account_name
      role_arn             = module.irsa_role.iam_role_arn
      policy               = var.policy
      domain_filters       = jsonencode(var.domain_filters)
      txt_owner_id         = var.txt_owner_id == "" ? var.cluster_name : var.txt_owner_id
      txt_prefix           = var.txt_prefix
    })
  ]
}
