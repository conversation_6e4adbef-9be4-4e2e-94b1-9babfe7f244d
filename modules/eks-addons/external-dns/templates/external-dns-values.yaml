# External-DNS Helm 配置文件
# 由 Terraform 生成

# AWS 提供者設置
provider: aws
aws:
  region: ${region}
  zoneType: ${zone_type}
  preferCNAME: true

# 服務帳號設置
serviceAccount:
  create: true
  name: ${service_account_name}
  annotations:
    eks.amazonaws.com/role-arn: ${role_arn}

# 來源設置 - 指定要監控的資源類型
sources:
  - service
  - ingress

# 額外參數 - 移除重複的 provider 設置
extraArgs:
  - --source=service
  - --source=ingress
  # 移除 --provider=aws，因為在 provider: aws 已經設置
  - --aws-prefer-cname
  - --txt-owner-id=${txt_owner_id}
  - --txt-prefix=${txt_prefix}

# 域名過濾器 - 限制 External-DNS 只管理特定域名
domainFilters: ${domain_filters}

# 策略 - 決定如何處理已存在的 DNS 記錄
policy: ${policy}

# 資源限制
resources:
  limits:
    cpu: 100m
    memory: 300Mi
  requests:
    cpu: 50m
    memory: 100Mi

# 安全環境設置
securityContext:
  fsGroup: 65534
  runAsNonRoot: true
  runAsUser: 65534

# 日誌設置
logLevel: info

# 同步間隔
interval: "1m"
