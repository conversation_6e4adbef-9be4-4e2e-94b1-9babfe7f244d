# EKS Addons 模組

## 概覽

此模組管理 Amazon EKS 叢集的附加元件和擴展功能，包括 Cluster Autoscaler、AWS Load Balancer Controller、External Secrets Operator 和 HashiCorp Vault。提供完整的 Kubernetes 生態系統支援，實現自動化運維和安全管理。

## 功能特性

- ✅ **Cluster Autoscaler**：自動調整節點群組大小
- ✅ **AWS Load Balancer Controller**：管理 ALB/NLB 負載均衡器
- ✅ **External Secrets Operator**：同步外部密碼到 Kubernetes
- ✅ **HashiCorp Vault**：企業級密碼管理和加密服務
- ✅ **IRSA 整合**：IAM Roles for Service Accounts 支援
- ✅ **Helm 部署**：使用 Helm Charts 進行標準化部署
- ✅ **可選部署**：靈活的元件啟用/停用配置
- ✅ **標籤管理**：完整的資源標籤支援

## 架構圖

```mermaid
graph TB
    subgraph "EKS 叢集"
        subgraph "kube-system"
            CA[Cluster Autoscaler]
            LBC[AWS Load Balancer Controller]
        end
        
        subgraph "external-secrets"
            ESO[External Secrets Operator]
        end
        
        subgraph "vault"
            VAULT[HashiCorp Vault]
            VAULT_UI[Vault UI]
        end
        
        subgraph "應用程式"
            APP1[應用程式 1]
            APP2[應用程式 2]
        end
    end
    
    subgraph "AWS 服務"
        ASG[Auto Scaling Groups]
        ALB[Application Load Balancer]
        SM[Secrets Manager]
        KMS[KMS 金鑰]
    end
    
    subgraph "外部服務"
        USERS[使用者]
        ADMIN[管理員]
    end
    
    CA --> ASG
    LBC --> ALB
    ESO --> SM
    VAULT --> KMS
    
    ALB --> APP1
    ALB --> APP2
    
    USERS --> ALB
    ADMIN --> VAULT_UI
    
    APP1 -.-> ESO
    APP2 -.-> VAULT
```

## 使用方式

### 基本使用

```hcl
module "eks_addons" {
  source = "./modules/eks-addons"

  cluster_name = module.eks.cluster_id
  
  # 啟用基本附加元件
  enable_cluster_autoscaler = true
  enable_aws_load_balancer_controller = true
  
  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### 完整配置

```hcl
module "eks_addons" {
  source = "./modules/eks-addons"

  cluster_name                = module.eks.cluster_id
  cluster_endpoint           = module.eks.cluster_endpoint
  cluster_certificate_authority_data = module.eks.cluster_certificate_authority_data
  oidc_provider_arn          = module.eks.oidc_provider_arn
  
  # 啟用所有附加元件
  enable_cluster_autoscaler           = true
  enable_aws_load_balancer_controller = true
  enable_external_secrets_operator    = true
  enable_vault                        = true
  
  # Vault 配置
  vault_kms_key_id = module.kms.kms_key_id
  vault_namespace  = "vault"
  
  tags = {
    Environment        = "prod"
    Project           = "production-app"
    CostCenter        = "engineering"
    DataClassification = "confidential"
  }
}
```

## 輸入變數

| 名稱 | 描述 | 類型 | 預設值 | 必要 |
|------|------|------|--------|------|
| `cluster_name` | EKS 叢集名稱 | `string` | - | ✅ |
| `cluster_endpoint` | EKS 叢集 API 端點 | `string` | `""` | ❌ |
| `cluster_certificate_authority_data` | 叢集 CA 憑證資料 | `string` | `""` | ❌ |
| `oidc_provider_arn` | OIDC 提供者 ARN | `string` | `""` | ❌ |
| `enable_cluster_autoscaler` | 是否啟用 Cluster Autoscaler | `bool` | `false` | ❌ |
| `enable_aws_load_balancer_controller` | 是否啟用 AWS Load Balancer Controller | `bool` | `false` | ❌ |
| `enable_external_secrets_operator` | 是否啟用 External Secrets Operator | `bool` | `false` | ❌ |
| `enable_vault` | 是否啟用 HashiCorp Vault | `bool` | `false` | ❌ |
| `vault_kms_key_id` | Vault KMS 金鑰 ID | `string` | `""` | ❌ |
| `vault_namespace` | Vault 命名空間 | `string` | `"vault"` | ❌ |
| `tags` | 資源標籤 | `map(string)` | `{}` | ❌ |

## 輸出值

| 名稱 | 描述 |
|------|------|
| `cluster_autoscaler_service_account_name` | Cluster Autoscaler 服務帳號名稱 |
| `aws_load_balancer_controller_service_account_name` | AWS Load Balancer Controller 服務帳號名稱 |
| `external_secrets_operator_service_account_name` | External Secrets Operator 服務帳號名稱 |
| `vault_service_name` | Vault 服務名稱 |
| `vault_namespace` | Vault 命名空間 |

## 附加元件詳情

### 1. Cluster Autoscaler
- **用途**：自動調整節點群組大小
- **命名空間**：kube-system
- **IRSA**：是
- **Helm Chart**：autoscaler/cluster-autoscaler

### 2. AWS Load Balancer Controller
- **用途**：管理 ALB/NLB 負載均衡器
- **命名空間**：kube-system
- **IRSA**：是
- **Helm Chart**：eks/aws-load-balancer-controller

### 3. External Secrets Operator
- **用途**：同步外部密碼到 Kubernetes
- **命名空間**：external-secrets
- **IRSA**：是
- **Helm Chart**：external-secrets/external-secrets

### 4. HashiCorp Vault
- **用途**：企業級密碼管理
- **命名空間**：vault（可配置）
- **IRSA**：是
- **Helm Chart**：hashicorp/vault

## 子模組

此模組包含以下子模組，每個都有詳細的文檔：

- [Cluster Autoscaler](./cluster-autoscaler/README.md)
- [AWS Load Balancer Controller](./aws-load-balancer-controller/README.md)
- [External Secrets Operator](./external-secrets-operator/README.md)
- [HashiCorp Vault](./vault/README.md)

## 最佳實踐

### 1. 附加元件選擇
```hcl
# 生產環境建議配置
module "eks_addons" {
  source = "./modules/eks-addons"
  
  cluster_name = module.eks.cluster_id
  
  # 核心附加元件
  enable_cluster_autoscaler           = true
  enable_aws_load_balancer_controller = true
  
  # 安全和密碼管理
  enable_external_secrets_operator = true
  enable_vault                     = true
  
  # Vault 配置
  vault_kms_key_id = module.kms.kms_key_id
}
```

### 2. 資源配置
- 為每個附加元件配置適當的資源限制
- 使用節點選擇器將系統元件部署到專用節點
- 配置適當的副本數以確保高可用性

### 3. 安全配置
- 使用 IRSA 進行細粒度權限控制
- 定期更新附加元件版本
- 監控附加元件的安全漏洞

### 4. 監控和日誌
- 配置適當的日誌級別
- 設置監控和警報
- 定期檢查附加元件狀態

## 故障排除

### 常見問題

#### 1. Helm 部署失敗
**問題**：Helm Chart 部署失敗

**解決方案**：
```bash
# 檢查 Helm 狀態
helm list -A

# 檢查特定發布
helm status <release-name> -n <namespace>

# 查看部署日誌
kubectl logs -n <namespace> -l app=<app-name>
```

#### 2. IRSA 權限問題
**問題**：服務帳號無法承擔 IAM 角色

**解決方案**：
```bash
# 檢查服務帳號註解
kubectl get sa <service-account> -n <namespace> -o yaml

# 檢查 Pod 的 AWS 憑證
kubectl exec -it <pod-name> -n <namespace> -- env | grep AWS
```

#### 3. 附加元件無法啟動
**問題**：附加元件 Pod 無法啟動

**解決方案**：
```bash
# 檢查 Pod 狀態
kubectl get pods -n <namespace>

# 查看 Pod 事件
kubectl describe pod <pod-name> -n <namespace>

# 檢查節點資源
kubectl top nodes
```

### 調試指令

```bash
# 檢查所有附加元件狀態
kubectl get pods -A | grep -E "(cluster-autoscaler|aws-load-balancer|external-secrets|vault)"

# 檢查 Helm 發布
helm list -A

# 檢查 IRSA 配置
kubectl get sa -A -o yaml | grep -A 5 -B 5 "eks.amazonaws.com/role-arn"

# 檢查節點標籤（用於 Cluster Autoscaler）
kubectl get nodes --show-labels
```

## 升級指南

### 1. 附加元件版本升級
```bash
# 檢查可用版本
helm search repo <chart-repo>/<chart-name> --versions

# 升級特定附加元件
helm upgrade <release-name> <chart-repo>/<chart-name> \
  --version <new-version> \
  --namespace <namespace>
```

### 2. 配置變更
- 使用 `terraform plan` 檢查變更
- 逐個升級附加元件以降低風險
- 在升級前備份重要配置

### 3. 回滾策略
```bash
# 檢查發布歷史
helm history <release-name> -n <namespace>

# 回滾到前一版本
helm rollback <release-name> <revision> -n <namespace>
```

## 成本優化

### 1. 資源配置
- 配置適當的資源請求和限制
- 使用節點親和性優化資源使用
- 定期檢查資源使用情況

### 2. 附加元件選擇
- 只啟用必要的附加元件
- 考慮使用 AWS 託管的替代方案
- 評估第三方解決方案的成本效益

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0
- **Kubernetes Provider**: ~> 2.20
- **Helm Provider**: ~> 2.5

## 相關資源

- [Amazon EKS 附加元件](https://docs.aws.amazon.com/eks/latest/userguide/eks-add-ons.html)
- [Kubernetes 官方文檔](https://kubernetes.io/docs/)
- [Helm 文檔](https://helm.sh/docs/)
- [IRSA 最佳實踐](https://docs.aws.amazon.com/eks/latest/userguide/iam-roles-for-service-accounts.html)

## 範例

查看 [examples](../../examples/) 目錄以獲取更多使用範例。
