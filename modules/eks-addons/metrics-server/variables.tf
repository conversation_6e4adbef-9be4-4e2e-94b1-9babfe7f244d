variable "cluster_name" {
  description = "EKS 集群名稱"
  type        = string
}

variable "addon_version" {
  description = "Metrics Server EKS Addon 版本"
  type        = string
  default     = "v0.7.2-eksbuild.4" # AWS EKS Addon 最新版本
}

variable "namespace" {
  description = "部署 Metrics Server 的命名空間"
  type        = string
  default     = "kube-system"
}

variable "create_namespace" {
  description = "是否建立 Metrics Server 命名空間"
  type        = bool
  default     = false
}

variable "tags" {
  description = "要與所有資源關聯的標籤"
  type        = map(string)
  default     = {}
}
