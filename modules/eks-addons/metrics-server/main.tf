locals {
  addon_name = "metrics-server"
}

# 使用 AWS EKS Addon 部署 Metrics Server
resource "aws_eks_addon" "metrics_server" {
  cluster_name                = var.cluster_name
  addon_name                  = local.addon_name
  addon_version               = var.addon_version
  preserve                    = false
  resolve_conflicts_on_update = "OVERWRITE"

  # 注意：EKS 附加元件版本的 Metrics Server 使用原生配置，
  # 確保附加元件使用適合的資源配置和設定

  tags = var.tags
}
