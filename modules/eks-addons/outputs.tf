# AWS Load Balancer Controller 相關輸出已移除

# Cluster Autoscaler 輸出
output "cluster_autoscaler_iam_role_arn" {
  description = "Cluster Autoscaler IAM 角色 ARN"
  value       = var.enable_cluster_autoscaler ? try(module.cluster_autoscaler[0].iam_role_arn, null) : null
}

output "cluster_autoscaler_iam_role_name" {
  description = "Cluster Autoscaler IAM 角色名稱"
  value       = var.enable_cluster_autoscaler ? try(module.cluster_autoscaler[0].iam_role_name, null) : null
}

output "cluster_autoscaler_service_account_name" {
  description = "Cluster Autoscaler Kubernetes 服務帳戶名稱"
  value       = var.enable_cluster_autoscaler ? try(module.cluster_autoscaler[0].service_account_name, null) : null
}

# External DNS 相關輸出已移除

# External Secrets 輸出

output "external_secrets_iam_role_arn" {
  description = "External Secrets IAM 角色 ARN"
  value       = var.enable_external_secrets ? try(module.external_secrets[0].role_arn, null) : null
}

output "external_secrets_iam_role_name" {
  description = "External Secrets IAM 角色名稱"
  value       = var.enable_external_secrets ? try(module.external_secrets[0].role_name, null) : null
}

# Vault 輸出
output "vault_enabled" {
  description = "是否啟用 Vault"
  value       = var.enable_vault
}

output "vault_namespace" {
  description = "Vault 命名空間"
  value       = var.enable_vault ? try(module.vault[0].vault_namespace, var.vault_namespace) : null
}

output "vault_service_name" {
  description = "Vault 服務名稱"
  value       = var.enable_vault ? try(module.vault[0].vault_service_name, "vault") : null
}

output "vault_service_port" {
  description = "Vault 服務埠口"
  value       = var.enable_vault ? try(module.vault[0].vault_service_port, 8200) : null
}

output "vault_iam_role_arn" {
  description = "Vault IAM 角色 ARN"
  value       = var.enable_vault ? try(module.vault[0].vault_iam_role_arn, null) : null
}

output "vault_root_token_secret_name" {
  description = "存儲 Vault Root Token 的 Secrets Manager 密鑰名稱"
  value       = var.enable_vault ? try(module.vault[0].vault_root_token_secret_name, "${var.cluster_name}-vault-root-token") : null
}

output "vault_recovery_keys_secret_name" {
  description = "存儲 Vault Recovery Keys 的 Secrets Manager 密鑰名稱"
  value       = var.enable_vault ? try(module.vault[0].vault_recovery_keys_secret_name, "${var.cluster_name}-vault-recovery-keys") : null
}

output "vault_root_token" {
  description = "Vault Root Token（敏感資訊）"
  value       = var.enable_vault ? try(module.vault[0].root_token, null) : null
  sensitive   = true
}

output "external_secrets_service_account_name" {
  description = "External Secrets Kubernetes 服務帳戶名稱"
  value       = var.enable_external_secrets ? try(module.external_secrets[0].service_account_name, null) : null
}

output "external_secrets_namespace" {
  description = "External Secrets 命名空間"
  value       = var.enable_external_secrets ? try(module.external_secrets[0].namespace, var.external_secrets_namespace) : null
}

# Metrics Server 輸出
output "metrics_server_namespace" {
  description = "Metrics Server 命名空間"
  value       = var.enable_metrics_server ? try(module.metrics_server[0].namespace, var.metrics_server_namespace) : null
}

output "metrics_server_addon_version" {
  description = "Metrics Server EKS Addon 版本"
  value       = var.enable_metrics_server ? try(var.metrics_server_addon_version, null) : null
}

output "metrics_server_release" {
  description = "Metrics Server Helm Release 名稱"
  value       = var.enable_metrics_server ? try(module.metrics_server[0].metrics_server_release, null) : null
}

# Reloader 輸出
output "reloader_namespace" {
  description = "Reloader 命名空間"
  value       = var.enable_reloader ? try(module.reloader[0].namespace, var.reloader_namespace) : null
}

output "reloader_release" {
  description = "Reloader Helm Release 名稱"
  value       = var.enable_reloader ? try(module.reloader[0].reloader_release, null) : null
}
