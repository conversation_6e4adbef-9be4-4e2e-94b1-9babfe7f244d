variable "cluster_name" {
  description = "EKS 叢集名稱"
  type        = string
}

variable "oidc_provider_arn" {
  description = "EKS OIDC Provider ARN"
  type        = string
}

variable "region" {
  description = "AWS 區域"
  type        = string
}

variable "chart_version" {
  description = "External Secrets Helm Chart 版本"
  type        = string
  default     = "0.9.9" # 請根據最新版本更新
}

variable "create_namespace" {
  description = "是否建立命名空間"
  type        = bool
  default     = true
}

variable "namespace" {
  description = "External Secrets 安裝的命名空間"
  type        = string
  default     = "external-secrets"
}

variable "service_account_name" {
  description = "External Secrets 服務帳號名稱"
  type        = string
  default     = "external-secrets"
}

variable "tags" {
  description = "資源標籤"
  type        = map(string)
  default     = {}
}
