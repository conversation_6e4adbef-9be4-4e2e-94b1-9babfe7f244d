locals {
  role_name = "${var.cluster_name}-external-secrets"
}

# 使用 IRSA 模組建立 IAM 角色
module "irsa_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "~> 5.30.0"

  role_name        = local.role_name
  role_description = "IAM role for External Secrets"
  role_policy_arns = { policy = aws_iam_policy.this.arn }

  oidc_providers = {
    eks = {
      provider_arn               = var.oidc_provider_arn
      namespace_service_accounts = ["${var.namespace}:${var.service_account_name}"]
    }
  }

  tags = var.tags
}

# 建立 IAM 政策
resource "aws_iam_policy" "this" {
  name        = local.role_name
  description = "IAM policy for External Secrets"
  policy      = file("${path.module}/iam-policy.json")
  tags        = var.tags
}

# 建立命名空間（如果需要）
resource "kubernetes_namespace" "this" {
  count = var.create_namespace ? 1 : 0

  metadata {
    name = var.namespace
    labels = {
      name = var.namespace
    }
  }
}

# 安裝 External Secrets Operator
resource "helm_release" "external_secrets" {
  name       = "external-secrets"
  repository = "https://charts.external-secrets.io"
  chart      = "external-secrets"
  version    = var.chart_version
  namespace  = var.namespace

  depends_on = [
    kubernetes_namespace.this,
    module.irsa_role
  ]

  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = module.irsa_role.iam_role_arn
    type  = "string"
  }

  set {
    name  = "webhook.port"
    value = "9443"
  }

  set {
    name  = "webhook.create"
    value = "true"
  }

  set {
    name  = "certController.create"
    value = "true"
  }

  set {
    name  = "resources.limits.cpu"
    value = "100m"
  }

  set {
    name  = "resources.limits.memory"
    value = "256Mi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "50m"
  }

  set {
    name  = "resources.requests.memory"
    value = "128Mi"
  }
}
