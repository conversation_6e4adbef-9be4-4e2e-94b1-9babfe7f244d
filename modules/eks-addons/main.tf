# 安裝 AWS Load Balancer Controller
module "aws_load_balancer_controller" {
  source = "./aws-load-balancer-controller"

  cluster_name      = var.cluster_name
  oidc_provider_arn = var.oidc_provider_arn
  oidc_provider_url = var.oidc_provider_url
  region            = var.region
  vpc_id            = var.vpc_id
  chart_version     = var.aws_load_balancer_controller_chart_version

  # 基本設定
  feature_gates                 = ""
  enable_shield                 = false
  enable_waf                    = false
  enable_wafv2                  = false
  default_ssl_policy            = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  default_target_type           = "ip"
  ingress_class                 = "alb"
  log_level                     = "info"
  default_access_logs_s3_bucket = ""
  default_access_logs_enabled   = false
  controller_sync_period        = 60
  webhook_timeout               = 30
  default_backend_protocol      = "HTTP"
  default_backend_port          = 80
  default_healthcheck_path      = "/"
  enable_leader_election        = true

  tags = var.tags
}

# 安裝 Cluster Autoscaler
module "cluster_autoscaler" {
  count  = var.enable_cluster_autoscaler ? 1 : 0
  source = "./cluster-autoscaler"

  cluster_name      = var.cluster_name
  region            = var.region
  oidc_provider_arn = var.oidc_provider_arn
  chart_version     = var.cluster_autoscaler_chart_version
  tags              = var.tags
}

# 安裝 External DNS
module "external_dns" {
  count  = var.enable_external_dns ? 1 : 0
  source = "./external-dns"

  cluster_name         = var.cluster_name
  region               = var.region
  oidc_provider_arn    = var.oidc_provider_arn
  chart_version        = var.external_dns_chart_version
  create_namespace     = var.external_dns_create_namespace
  namespace            = var.external_dns_namespace
  service_account_name = var.external_dns_service_account_name
  domain_filters       = var.external_dns_domain_filters
  policy               = var.external_dns_policy
  hosted_zone_arns     = var.external_dns_hosted_zone_arns
  zone_type            = var.external_dns_zone_type
  txt_owner_id         = var.external_dns_txt_owner_id
  txt_prefix           = var.external_dns_txt_prefix
  tags                 = var.tags
}

# 安裝 External Secrets
module "external_secrets" {
  count  = var.enable_external_secrets ? 1 : 0
  source = "./external-secrets"

  cluster_name      = var.cluster_name
  region            = var.region
  oidc_provider_arn = var.oidc_provider_arn
  chart_version     = var.external_secrets_chart_version
  create_namespace  = var.external_secrets_create_namespace
  namespace         = var.external_secrets_namespace
  tags              = var.tags
}

# 安裝 Kubernetes Metrics Server (使用 EKS 原生附加元件)
module "metrics_server" {
  count  = var.enable_metrics_server ? 1 : 0
  source = "./metrics-server"

  cluster_name     = var.cluster_name
  addon_version    = var.metrics_server_addon_version
  namespace        = var.metrics_server_namespace
  create_namespace = var.metrics_server_create_namespace
  tags             = var.tags
}

# 安裝 Reloader
module "reloader" {
  count  = var.enable_reloader ? 1 : 0
  source = "./reloader"

  cluster_name     = var.cluster_name
  chart_version    = var.reloader_chart_version
  create_namespace = var.reloader_create_namespace
  namespace        = var.reloader_namespace
  tags             = var.tags
}

# 安裝 Vault
module "vault" {
  count  = var.enable_vault ? 1 : 0
  source = "./vault"

  cluster_name          = var.cluster_name
  region                = var.region
  oidc_provider_arn     = var.oidc_provider_arn
  vpc_id                = var.vpc_id
  kms_key_id            = var.vault_awskms_key_id
  eks_oidc_provider_url = try(replace(var.eks_oidc_provider_url, "https://", ""), var.eks_oidc_provider_url)
  private_subnet_ids    = var.private_subnet_ids

  # Vault 設定
  namespace = var.vault_namespace
  replicas  = var.vault_replicas

  # 為 Vault 提供 EKS 節點角色 ARN 以設定 KMS 金鑰權限
  eks_node_role_arns = var.eks_node_role_arns

  # 添加對 AWS Load Balancer Controller 的依賴
  aws_load_balancer_controller_enabled    = var.enable_aws_load_balancer_controller
  aws_load_balancer_controller_dependency = var.enable_aws_load_balancer_controller ? module.aws_load_balancer_controller : null

  tags = var.tags
}
