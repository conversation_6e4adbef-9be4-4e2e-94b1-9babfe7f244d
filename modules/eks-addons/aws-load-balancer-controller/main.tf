locals {
  name = "aws-load-balancer-controller"

  # 追蹤標籤
  default_tags = merge(
    var.tags,
    {
      "terraform-module"                          = "eks-addons/aws-load-balancer-controller"
      "terraform-region"                          = var.region
      "kubernetes.io/cluster/${var.cluster_name}" = "owned"
      "k8s.io/cluster"                            = var.cluster_name
    }
  )

  # 檢查是否有 ALB 實例
  has_albs = length(var.albs) > 0 ? true : false

  # 標準化 OIDC 提供者 URL 格式，去除最後的斜線
  oidc_provider_url_normalized = replace(var.oidc_provider_url, "https://", "")

  # 服務帳號名稱
  service_account_name = "aws-load-balancer-controller"
}

# 使用 IAM 模組為服務帳號創建角色
module "irsa_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "~> 5.30.0"

  role_name                              = "${var.cluster_name}-alb-controller"
  attach_load_balancer_controller_policy = true

  oidc_providers = {
    eks = {
      provider_arn               = var.oidc_provider_arn
      namespace_service_accounts = ["kube-system:${local.service_account_name}"]
    }
  }

  tags = local.default_tags
}

# 添加額外的標籤權限內聚政策 (如果需要)
resource "aws_iam_role_policy" "add_tags_policy" {
  name = "AddTagsPolicy"
  role = module.irsa_role.iam_role_name
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "elasticloadbalancing:AddTags",
          "elasticloadbalancing:RemoveTags",
          "wafv2:GetWebACLForResource",
          "shield:GetSubscriptionState"
        ]
        Resource = "*"
      }
    ]
  })
}

# 使用 Helm 安裝 AWS Load Balancer Controller
resource "helm_release" "aws_load_balancer_controller" {
  name       = local.name
  repository = "https://aws.github.io/eks-charts"
  chart      = "aws-load-balancer-controller"
  namespace  = "kube-system"
  version    = var.chart_version

  # 可靠部署設置
  recreate_pods   = true
  atomic          = true
  cleanup_on_fail = true
  wait            = true
  wait_for_jobs   = true
  timeout         = 300

  values = [
    yamlencode({
      clusterName = var.cluster_name
      region      = var.region
      vpcId       = var.vpc_id

      serviceAccount = {
        create = true
        name   = local.service_account_name
        annotations = {
          "eks.amazonaws.com/role-arn" = module.irsa_role.iam_role_arn
        }
      }

      podLabels = {
        "app.kubernetes.io/name"       = "aws-load-balancer-controller"
        "app.kubernetes.io/managed-by" = "terraform"
      }

      # 資源請求和限制
      resources = {
        limits = {
          cpu    = "200m"
          memory = "256Mi"
        }
        requests = {
          cpu    = "100m"
          memory = "128Mi"
        }
      }

      # 啟用 Prometheus 指標
      metrics = {
        enabled = true
        service = {
          annotations = {
            "prometheus.io/scrape" = "true"
            "prometheus.io/port"   = "8080"
          }
        }
      }

      # 高可用性設置
      replicaCount = 2

      # 啟用容忍和親和性
      tolerations = [
        {
          key      = "node-role.kubernetes.io/control-plane"
          operator = "Exists"
          effect   = "NoSchedule"
        }
      ]

      # 健康檢查設置
      livenessProbe = {
        failureThreshold    = 3
        initialDelaySeconds = 30
        periodSeconds       = 30
        successThreshold    = 1
        timeoutSeconds      = 10
      }

      readinessProbe = {
        failureThreshold    = 3
        initialDelaySeconds = 30
        periodSeconds       = 30
        successThreshold    = 1
        timeoutSeconds      = 10
      }

      # 啟用 Pod Disruption Budget
      podDisruptionBudget = {
        enabled      = true
        minAvailable = 1
      }
    })
  ]

  depends_on = [
    module.irsa_role
  ]
}

# 使用 Kubernetes ConfigMap 配置 AWS Load Balancer Controller 的 ALB 設定
resource "kubernetes_config_map" "alb_config" {
  metadata {
    name      = "aws-load-balancer-controller-config"
    namespace = "kube-system"
  }

  data = {
    "feature-gates"                 = var.feature_gates
    "enable-shield"                 = tostring(var.enable_shield)
    "enable-waf"                    = tostring(var.enable_waf)
    "enable-wafv2"                  = tostring(var.enable_wafv2)
    "default-ssl-policy"            = var.default_ssl_policy
    "default-tags"                  = jsonencode(local.default_tags)
    "default-target-type"           = var.default_target_type
    "ingress-class"                 = var.ingress_class
    "log-level"                     = var.log_level
    "default-access-logs-s3-bucket" = var.default_access_logs_s3_bucket
    "default-access-logs-enabled"   = tostring(var.default_access_logs_enabled)
    "sync-period"                   = "${var.controller_sync_period}s"
    "webhook-timeout"               = "${var.webhook_timeout}s"
    "default-backend-protocol"      = var.default_backend_protocol
    "default-backend-port"          = tostring(var.default_backend_port)
    "default-healthcheck-path"      = var.default_healthcheck_path
    "enable-leader-election"        = tostring(var.enable_leader_election)
  }

  depends_on = [
    helm_release.aws_load_balancer_controller
  ]
}
# ALB 相關資源已移除
