variable "cluster_name" {
  description = "EKS 叢集名稱"
  type        = string
}

variable "oidc_provider_arn" {
  description = "EKS OIDC 提供者的 ARN"
  type        = string
}

variable "oidc_provider_url" {
  description = "EKS OIDC 提供者的 URL"
  type        = string
}

variable "region" {
  description = "AWS 區域"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID，用於 AWS Load Balancer Controller"
  type        = string
}

variable "chart_version" {
  description = "AWS Load Balancer Controller Helm Chart 版本"
  type        = string
  default     = "1.7.2"
}

variable "tags" {
  description = "要套用到所有資源的標籤"
  type        = map(string)
  default     = {}
}

# ALB Controller 配置變數
variable "feature_gates" {
  description = "AWS Load Balancer Controller 特性閘設定"
  type        = string
  default     = ""
}

variable "enable_shield" {
  description = "是否啟用 AWS Shield"
  type        = bool
  default     = false
}

variable "enable_waf" {
  description = "是否啟用 AWS WAF"
  type        = bool
  default     = false
}

variable "enable_wafv2" {
  description = "是否啟用 AWS WAFv2"
  type        = bool
  default     = false
}

variable "default_ssl_policy" {
  description = "預設 SSL 政策"
  type        = string
  default     = "ELBSecurityPolicy-2016-08"
}

variable "default_target_type" {
  description = "預設目標類型"
  type        = string
  default     = "ip"
}

variable "ingress_class" {
  description = "Ingress 類別"
  type        = string
  default     = "alb"
}

variable "log_level" {
  description = "控制器日誌層級"
  type        = string
  default     = "info"
}

variable "default_access_logs_s3_bucket" {
  description = "預設 ALB 存取日誌 S3 儲存桶"
  type        = string
  default     = ""
}

variable "default_access_logs_enabled" {
  description = "是否默認啟用 ALB 存取日誌"
  type        = bool
  default     = false
}

variable "controller_sync_period" {
  description = "控制器同步週期（秒）"
  type        = number
  default     = 60
}

variable "webhook_timeout" {
  description = "Webhook 逾時（秒）"
  type        = number
  default     = 30
}

variable "default_backend_protocol" {
  description = "預設後端協議"
  type        = string
  default     = "HTTP"
}

variable "default_backend_port" {
  description = "預設後端連接埠"
  type        = number
  default     = 80
}

variable "default_healthcheck_path" {
  description = "預設健康檢查路徑"
  type        = string
  default     = "/"
}

variable "enable_leader_election" {
  description = "是否啟用領導者選舉"
  type        = bool
  default     = true
}

# ALB 定義
variable "albs" {
  description = "要建立的 ALB 資源定義"
  type        = map(any)
  default     = {}
}

# DNS 和憑證設定
variable "route53_records" {
  description = "Route53 DNS 記錄，用於指向 ALB"
  type = map(object({
    zone_id      = string
    name         = string
    alb_dns_name = string
    alb_zone_id  = string
  }))
  default = {}
}

variable "acm_certificate_arns" {
  description = "要與 ALB 監聽器關聯的 ACM 憑證 ARN"
  type = map(object({
    dns_names = list(string)
  }))
  default = {}
}
