output "iam_role_arn" {
  description = "AWS Load Balancer Controller IAM 角色 ARN"
  value       = module.irsa_role.iam_role_arn
}

output "iam_role_name" {
  description = "AWS Load Balancer Controller IAM 角色名稱"
  value       = module.irsa_role.iam_role_name
}

# 注意：IRSA 模組不再直接暴露政策 ARN，因為政策直接綁定在角色上
# 如果需要 IAM 政策 ARN，可通過 AWS API/Console 查詢

output "helm_release_name" {
  description = "AWS Load Balancer Controller Helm Release 名稱"
  value       = helm_release.aws_load_balancer_controller.name
}

output "helm_release_status" {
  description = "AWS Load Balancer Controller Helm Release 狀態"
  value       = helm_release.aws_load_balancer_controller.status
}

output "namespace" {
  description = "AWS Load Balancer Controller 服務的命名空間"
  value       = "kube-system"
}

output "service_account_name" {
  description = "AWS Load Balancer Controller 服務帳戶名稱"
  value       = "aws-load-balancer-controller"
}

# ALB 相關輸出已移除
output "alb_config_map_name" {
  description = "ALB 控制器設定 ConfigMap 名稱"
  value       = kubernetes_config_map.alb_config.metadata[0].name
}
