# AWS Load Balancer Controller 的預設值配置
# 此文件為參考用，實際值將透過 Terraform 動態設定

# 基本配置
clusterName: "${cluster_name}"
region: "${region}"
vpcId: "${vpc_id}"

# 服務帳戶設定
serviceAccount:
  create: true
  name: "aws-load-balancer-controller"
  annotations:
    eks.amazonaws.com/role-arn: "${role_arn}"

# Pod 標籤
podLabels:
  app.kubernetes.io/name: "aws-load-balancer-controller"
  app.kubernetes.io/managed-by: "terraform"

# 資源設定
resources:
  limits:
    cpu: "200m"
    memory: "256Mi"
  requests:
    cpu: "100m"
    memory: "128Mi"

# 監控設定
metrics:
  enabled: true
  service:
    annotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "8080"

# 高可用性設定
replicaCount: 2

# 容錯設定
tolerations:
  - key: "node-role.kubernetes.io/control-plane"
    operator: "Exists"
    effect: "NoSchedule"

# 健康檢查設定
livenessProbe:
  failureThreshold: 3
  initialDelaySeconds: 30
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 10

readinessProbe:
  failureThreshold: 3
  initialDelaySeconds: 30
  periodSeconds: 30
  successThreshold: 1
  timeoutSeconds: 10

# Pod Disruption Budget 設定
podDisruptionBudget:
  enabled: true
  minAvailable: 1
