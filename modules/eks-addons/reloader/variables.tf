variable "cluster_name" {
  description = "EKS 叢集名稱"
  type        = string
}

variable "namespace" {
  description = "Reloader 的 Kubernetes 命名空間"
  type        = string
  default     = "kube-system"
}

variable "create_namespace" {
  description = "是否建立 Reloader 命名空間"
  type        = bool
  default     = false
}

variable "chart_version" {
  description = "Reloader Helm Chart 版本"
  type        = string
}

variable "service_account_name" {
  description = "Reloader 服務帳號名稱"
  type        = string
  default     = "reloader"
}

variable "tags" {
  description = "資源標籤"
  type        = map(string)
  default     = {}
}
