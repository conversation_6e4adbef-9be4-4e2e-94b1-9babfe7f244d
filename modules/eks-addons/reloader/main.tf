# 建立命名空間（如果需要）
resource "kubernetes_namespace" "this" {
  count = var.create_namespace ? 1 : 0

  metadata {
    name = var.namespace
    labels = {
      name = var.namespace
    }
  }
}

# 安裝 Reloader
resource "helm_release" "reloader" {
  name       = "reloader"
  repository = "https://stakater.github.io/stakater-charts"
  chart      = "reloader"
  version    = var.chart_version
  namespace  = var.namespace

  depends_on = [
    kubernetes_namespace.this
  ]

  set {
    name  = "reloader.watchGlobally"
    value = "true"
  }

  set {
    name  = "serviceAccount.name"
    value = var.service_account_name
  }

  set {
    name  = "serviceAccount.create"
    value = "true"
  }

  set {
    name  = "reloader.resources.limits.memory"
    value = "200Mi"
  }

  set {
    name  = "reloader.resources.limits.cpu"
    value = "100m"
  }

  set {
    name  = "reloader.resources.requests.memory"
    value = "100Mi"
  }

  set {
    name  = "reloader.resources.requests.cpu"
    value = "50m"
  }
}
