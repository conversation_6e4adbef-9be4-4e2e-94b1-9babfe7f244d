output "vault_namespace" {
  description = "Vault 所在的命名空間"
  value       = var.namespace
}

output "vault_service_name" {
  description = "Vault 服務名稱"
  value       = "vault"
}

output "vault_service_port" {
  description = "Vault 服務連接埠"
  value       = 8200
}

output "vault_iam_role_arn" {
  description = "用於 Vault 的 IAM 角色 ARN"
  value       = module.irsa_role.iam_role_arn
}

output "vault_root_token_secret_name" {
  description = "包含 Vault root token 的 AWS Secrets Manager 秘密名稱"
  value       = try(aws_secretsmanager_secret.vault_root_token.name, "${var.cluster_name}-vault-root-token")
}

output "vault_recovery_keys_secret_name" {
  description = "包含 Vault recovery keys 的 AWS Secrets Manager 秘密名稱"
  value       = try(aws_secretsmanager_secret.vault_recovery_keys.name, "${var.cluster_name}-vault-recovery-keys")
}

output "root_token_secret_name" {
  description = "存儲 Vault Root Token 的 Secrets Manager 密鑰名稱格式"
  value       = "${var.cluster_name}-vault-root-token-*"
}

# 提供安全的 root token 訪問指令
output "root_token" {
  description = "Vault Root Token 訪問指令（敏感資訊）"
  value = try(
    aws_secretsmanager_secret.vault_root_token.name != null ?
    "aws secretsmanager get-secret-value --secret-id ${aws_secretsmanager_secret.vault_root_token.name} --query 'SecretString' --output text" :
    "Vault root token secret not yet created",
    "Vault root token secret not available"
  )
  sensitive = true
}

# 提供 Vault 狀態檢查指令
output "vault_status_command" {
  description = "檢查 Vault 狀態的 kubectl 指令"
  value       = "kubectl exec -n ${var.namespace} vault-0 -- vault status"
}
