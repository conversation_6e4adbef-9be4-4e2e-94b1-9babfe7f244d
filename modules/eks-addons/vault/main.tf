locals {
  service_account_name = "vault"
  kms_policy_name      = "${var.cluster_name}-vault-kms-policy"
}

# 為 Vault 創建 IRSA 角色以便存取 AWS KMS
module "irsa_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "~> 5.30.0"

  role_name        = "${var.cluster_name}-vault"
  role_description = "IAM role for Vault"

  oidc_providers = {
    eks = {
      provider_arn               = var.oidc_provider_arn
      namespace_service_accounts = ["${var.namespace}:${local.service_account_name}"]
    }
  }

  tags = var.tags
}

# 建立 Vault 存取 KMS 的政策
resource "aws_iam_policy" "vault_kms" {
  name        = local.kms_policy_name
  description = "Policy for Vault to use AWS KMS for auto-unseal"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:DescribeKey",
          "kms:CreateGrant",
          "kms:ListGrants",
          "kms:RevokeGrant",
          "kms:GenerateDataKey"
        ]
        Resource = "arn:aws:kms:${var.region}:*:key/${var.kms_key_id}"
      }
    ]
  })

  tags = var.tags
}

# 確保 KMS 金鑰政策允許 EKS 節點角色存取
data "aws_caller_identity" "current" {}

resource "aws_kms_key_policy" "vault_kms_policy" {
  key_id = var.kms_key_id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Default"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "KeyOwner"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "KeyAdministration"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action = [
          "kms:Update*",
          "kms:UntagResource",
          "kms:TagResource",
          "kms:ScheduleKeyDeletion",
          "kms:Revoke*",
          "kms:ReplicateKey",
          "kms:Put*",
          "kms:List*",
          "kms:ImportKeyMaterial",
          "kms:Get*",
          "kms:Enable*",
          "kms:Disable*",
          "kms:Describe*",
          "kms:Delete*",
          "kms:Create*",
          "kms:CancelKeyDeletion"
        ]
        Resource = "*"
      },
      {
        Sid    = "KeyUsage"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action = [
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:Encrypt",
          "kms:DescribeKey",
          "kms:Decrypt"
        ]
        Resource = "*"
      },
      {
        Sid    = "VaultIRSAAccess"
        Effect = "Allow"
        Principal = {
          AWS = module.irsa_role.iam_role_arn
        }
        Action = [
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:Encrypt",
          "kms:DescribeKey",
          "kms:Decrypt",
          "kms:CreateGrant",
          "kms:ListGrants",
          "kms:RevokeGrant"
        ]
        Resource = "*"
      },
      {
        Sid    = "EKSNodeAccess"
        Effect = "Allow"
        Principal = {
          AWS = var.eks_node_role_arns
        }
        Action = [
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:Encrypt",
          "kms:DescribeKey",
          "kms:Decrypt",
          "kms:CreateGrant",
          "kms:ListGrants",
          "kms:RevokeGrant"
        ]
        Resource = "*"
      }
    ]
  })
}

# 將 KMS 政策附加到 IRSA 角色
resource "aws_iam_role_policy_attachment" "vault_kms" {
  role       = module.irsa_role.iam_role_name
  policy_arn = aws_iam_policy.vault_kms.arn
}

# 建立 Vault 命名空間
resource "kubernetes_namespace" "vault" {
  metadata {
    name = var.namespace
    labels = {
      name = var.namespace
    }
  }
}

# 部署 Vault
resource "helm_release" "vault" {
  name       = "vault"
  repository = "https://helm.releases.hashicorp.com"
  chart      = "vault"
  version    = var.chart_version
  namespace  = kubernetes_namespace.vault.metadata[0].name

  # 等待 Pod 就緒
  wait    = true
  timeout = 600

  values = [
    templatefile("${path.module}/templates/vault-values.yaml", {
      replicas          = var.replicas
      service_account   = local.service_account_name
      region            = var.region
      kms_key_id        = var.kms_key_id
      iam_role_arn      = module.irsa_role.iam_role_arn
      eks_oidc_provider = var.eks_oidc_provider_url
    })
  ]

  depends_on = [
    kubernetes_namespace.vault,
    module.irsa_role,
    var.aws_load_balancer_controller_dependency
  ]
}

# 創建 AWS Secrets Manager Secret 資源來存儲 Vault root token
resource "aws_secretsmanager_secret" "vault_root_token" {
  name        = "${var.cluster_name}-vault-root-token-${formatdate("YYYYMMDDHHmmss", timestamp())}"
  description = "Vault root token for ${var.cluster_name} created at ${formatdate("YYYY-MM-DD HH:mm:ss", timestamp())}"
  tags        = var.tags
}

# 創建 AWS Secrets Manager Secret 資源來存儲 Vault recovery keys
resource "aws_secretsmanager_secret" "vault_recovery_keys" {
  name        = "${var.cluster_name}-vault-recovery-keys-${formatdate("YYYYMMDDHHmmss", timestamp())}"
  description = "Vault recovery keys for ${var.cluster_name} created at ${formatdate("YYYY-MM-DD HH:mm:ss", timestamp())}"
  tags        = var.tags
}

# Vault 初始化並保存 root token 和 recovery keys 到 Secrets Manager
resource "null_resource" "vault_init" {
  triggers = {
    helm_release      = helm_release.vault.id
    namespace         = var.namespace
    cluster_name      = var.cluster_name
    root_token_arn    = aws_secretsmanager_secret.vault_root_token.arn
    recovery_keys_arn = aws_secretsmanager_secret.vault_recovery_keys.arn
    region            = var.region
  }

  # 在建立時初始化 Vault 並將 token 和 recovery keys 保存到 Secrets Manager
  provisioner "local-exec" {
    interpreter = ["/bin/bash", "-c"]
    command     = <<-EOT
      # 等待 Vault pod 準備就緒
      echo "等待 Vault Pod 就緒..."
      kubectl wait --for=condition=Ready pod/vault-0 -n ${var.namespace} --timeout=300s || true
      kubectl wait --for=condition=Ready pod/vault-1 -n ${var.namespace} --timeout=300s || true
      kubectl wait --for=condition=Ready pod/vault-2 -n ${var.namespace} --timeout=300s || true
      
      # 檢查 Vault 是否已初始化
      INIT_STATUS=$(kubectl exec -n ${var.namespace} vault-0 -- vault status -format=json 2>/dev/null | jq -r '.initialized' 2>/dev/null || echo "false")
      
      # 檢查 Vault 是否已解封
      SEAL_STATUS=$(kubectl exec -n ${var.namespace} vault-0 -- vault status -format=json 2>/dev/null | jq -r '.sealed' 2>/dev/null || echo "true")
      
      if [ "$INIT_STATUS" != "true" ]; then
        echo "初始化 Vault..."
        # 執行初始化並保存結果
        INIT_OUTPUT=$(kubectl exec -n ${var.namespace} vault-0 -- vault operator init -format=json)
        
        # 提取 root token 和 recovery keys
        ROOT_TOKEN=$(echo $INIT_OUTPUT | jq -r '.root_token')
        RECOVERY_KEYS=$(echo $INIT_OUTPUT | jq -r '.recovery_keys_b64 | join("\n")')
        
        # 保存到 Secrets Manager
        echo "保存 root token 到 Secrets Manager..."
        aws secretsmanager put-secret-value \
          --secret-id "${aws_secretsmanager_secret.vault_root_token.id}" \
          --secret-string "$ROOT_TOKEN" \
          --region ${var.region}
        
        # 確認 token 已成功保存
        echo "確認 root token 已保存到 Secrets Manager..."
        for i in {1..10}; do
          TOKEN_CHECK=$(aws secretsmanager get-secret-value \
            --secret-id "${aws_secretsmanager_secret.vault_root_token.id}" \
            --query 'SecretString' \
            --output text \
            --region ${var.region} 2>/dev/null || echo "")
          
          if [ -n "$TOKEN_CHECK" ]; then
            echo "Root token 已成功保存到 Secrets Manager: $TOKEN_CHECK"
            
            # 將 token 寫入臨時文件，以便 Terraform 輸出可以使用
            echo "$ROOT_TOKEN" > /tmp/${var.cluster_name}_vault_token
            chmod 600 /tmp/${var.cluster_name}_vault_token
            
            break
          else
            echo "等待 root token 在 Secrets Manager 中可用 (嘗試 $i/10)..."
            sleep 10
          fi
        done
          
        echo "保存 recovery keys 到 Secrets Manager..."
        aws secretsmanager put-secret-value \
          --secret-id "${aws_secretsmanager_secret.vault_recovery_keys.id}" \
          --secret-string "$RECOVERY_KEYS" \
          --region ${var.region}
          
        echo "Vault 初始化完成，憑證已保存到 Secrets Manager。"
        
        # 自動解封 Vault
        echo "正在自動解封 Vault..."
        
        # 從 Secrets Manager 獲取 recovery keys
        echo "從 Secrets Manager 獲取 recovery keys..."
        RECOVERY_KEYS=$(aws secretsmanager get-secret-value \
          --secret-id "${aws_secretsmanager_secret.vault_recovery_keys.id}" \
          --region ${var.region} \
          --query 'SecretString' \
          --output text | jq -r '.[]')
        
        # 使用前三個 recovery keys 解封 Vault
        echo "使用 recovery keys 解封 Vault..."
        for i in 0 1 2; do
          echo "解封 vault-$i..."
          # 使用前三個 recovery keys 進行解封
          for key in $(echo "$RECOVERY_KEYS" | head -n 3); do
            kubectl exec -n ${var.namespace} vault-$i -- vault operator unseal $key || true
          done
        done
        
      else
        echo "Vault 已初始化，檢查是否需要解封..."
        
        # 如果 Vault 已初始化但處於封裝狀態，則嘗試解封
        if [ "$SEAL_STATUS" = "true" ]; then
          echo "Vault 處於封裝狀態，正在嘗試自動解封..."
          
          # 從 Secrets Manager 獲取 recovery keys
          echo "從 Secrets Manager 獲取 recovery keys..."
          RECOVERY_KEYS=$(aws secretsmanager get-secret-value \
            --secret-id "${aws_secretsmanager_secret.vault_recovery_keys.id}" \
            --region ${var.region} \
            --query 'SecretString' \
            --output text | jq -r '.[]')
          
          # 使用前三個 recovery keys 解封 Vault
          echo "使用 recovery keys 解封 Vault..."
          for i in 0 1 2; do
            echo "解封 vault-$i..."
            # 使用前三個 recovery keys 進行解封
            for key in $(echo "$RECOVERY_KEYS" | head -n 3); do
              kubectl exec -n ${var.namespace} vault-$i -- vault operator unseal $key || true
            done
          done
        fi
        
        # 檢查 Secrets Manager 中是否已有 root token
        if ! aws secretsmanager describe-secret --secret-id "${aws_secretsmanager_secret.vault_root_token.id}" --region ${var.region} &> /dev/null; then
          echo "在 Secrets Manager 找不到 token，從 Vault 讀取並保存..."
          # 在這種情況下，可能需要手動從 Vault 取得 token 並保存
          # 實際情況中可能需要其他方式來處理此情況
          echo "警告: Vault 已初始化但 Secrets Manager 中沒有 token，可能需要手動介入"
        fi
        
        # 從 Secrets Manager 獲取 root token
        echo "從 Secrets Manager 獲取 root token..."
        ROOT_TOKEN=$(aws secretsmanager get-secret-value \
          --secret-id "${aws_secretsmanager_secret.vault_root_token.id}" \
          --region ${var.region} \
          --query 'SecretString' \
          --output text)
      fi
      
      # 等待所有 Vault Pod 運行
      echo "等待所有 Vault Pod 運行..."
      for i in 0 1 2; do
        kubectl wait --for=condition=Ready pod/vault-$i -n ${var.namespace} --timeout=10s || true
      done
      
      # 檢查 Raft 叢集狀態並自動將副本節點加入叢集
      echo "檢查 Raft 叢集狀態..."
      
      # 設置 VAULT_TOKEN 環境變數
      export VAULT_TOKEN="$ROOT_TOKEN"
      
      # 獲取當前叢集中的節點
      CURRENT_PEERS=$(kubectl exec -n ${var.namespace} vault-0 -- sh -c "export VAULT_TOKEN=$ROOT_TOKEN && vault operator raft list-peers -format=json" | jq -r '.data.config.servers[].node_id')
      
      # 將副本節點加入 Raft 叢集
      for replica in 1 2; do
        if ! echo "$CURRENT_PEERS" | grep -q "vault-$replica"; then
          echo "將 vault-$replica 加入 Raft 叢集..."
          kubectl exec -n ${var.namespace} vault-$replica -- vault operator raft join http://vault-0.vault-internal:8200 || true
          echo "vault-$replica 已加入叢集"
        else
          echo "vault-$replica 已在叢集中"
        fi
      done
      
      # 顯示最終叢集狀態
      echo "Raft 叢集最終狀態:"
      kubectl exec -n ${var.namespace} vault-0 -- sh -c "export VAULT_TOKEN=$ROOT_TOKEN && vault operator raft list-peers"

      echo "Vault 叢集設置完成。"
    EOT
  }

  depends_on = [
    aws_secretsmanager_secret.vault_root_token,
    aws_secretsmanager_secret.vault_recovery_keys,
    helm_release.vault
  ]
}