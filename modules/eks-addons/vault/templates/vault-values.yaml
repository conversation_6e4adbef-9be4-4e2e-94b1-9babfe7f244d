injector:
  enabled: false  

server:
  auditStorage:
    enabled: false

  persistentVolumeClaimRetentionPolicy:
    whenDeleted: Delete

  # 指定持久卷相關設定
  dataStorage:
    enabled: true
    storageClass: "gp2"
    accessMode: ReadWriteOnce
    size: 10Gi
  
  # 配置高可用性
  ha:
    enabled: true
    replicas: ${replicas}
    raft:
      enabled: true
      setNodeId: true
      config: |
        ui = true

        listener "tcp" {
          address = "[::]:8200"
          cluster_address = "[::]:8201"
          tls_disable = true
        }
        
        storage "raft" {
          path = "/vault/data"
        }
        
        seal "awskms" {
          region     = "${region}"
          kms_key_id = "arn:aws:kms:${region}:920372997208:key/${kms_key_id}"
          role_arn   = "${iam_role_arn}"
        }
        
        service_registration "kubernetes" {}
  
  # 配置 Affinity 確保 Pod 分散在不同節點上
  affinity: |
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchLabels:
              app.kubernetes.io/name: vault
              component: server
          topologyKey: kubernetes.io/hostname
  
  # 配置環境變數以強制使用 IRSA
  extraEnvironmentVars:
    AWS_SDK_LOAD_CONFIG: "true"
    AWS_STS_REGIONAL_ENDPOINTS: "regional"

  # 配置服務帳戶
  serviceAccount:
    create: true
    name: "${service_account}"
    annotations:
      eks.amazonaws.com/role-arn: "${iam_role_arn}"
  
  # 添加 Pod 安全設置
  securityContext:
    runAsNonRoot: true
    runAsUser: 100
    runAsGroup: 1000
    fsGroup: 1000
  
  # 設置資源限制
  resources:
    requests:
      memory: "256Mi"
      cpu: "250m"
    limits:
      memory: "512Mi"
      cpu: "500m"
      
  # 添加 Ingress 配置
  ingress:
    enabled: true
    ingressClassName: "alb"
    hosts:
      - host: "vault.longshun.io"
    annotations:
      kubernetes.io/ingress.class: alb
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
      alb.ingress.kubernetes.io/ssl-redirect: '443'
      alb.ingress.kubernetes.io/healthcheck-path: /ui/
      alb.ingress.kubernetes.io/success-codes: '200,204,302'
      alb.ingress.kubernetes.io/group.name: vault
      # 禁用 WAF 關聯
      alb.ingress.kubernetes.io/wafv2-acl-arn: ""
      # External-DNS 相關配置
      external-dns.alpha.kubernetes.io/hostname: vault.longshun.io
      external-dns.alpha.kubernetes.io/ttl: "300"
      external-dns.alpha.kubernetes.io/aws-weight: "100"
      # 確保 External-DNS 能夠識別此 Ingress
      external-dns/is-public: "true"

  # 配置 Pod Disruption Budget
  disruptionBudget:
    enabled: true
    maxUnavailable: 1  # 允許最多 1 個 pod 不可用，確保 node group 更新時不會卡住

ui:
  enabled: true
  serviceType: "ClusterIP"
  externalPort: 8200
