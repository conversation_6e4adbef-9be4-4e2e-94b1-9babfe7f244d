variable "region" {
  description = "AWS 區域"
  type        = string
}

variable "cluster_name" {
  description = "EKS 叢集名稱"
  type        = string
}

variable "oidc_provider_arn" {
  description = "EKS OIDC 提供者的 ARN"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID"
  type        = string
}

variable "kms_key_id" {
  description = "用於 Vault 自動解封的 KMS 金鑰 ID"
  type        = string
}

variable "eks_oidc_provider_url" {
  description = "EKS OIDC 提供者 URL，不含協議前綴"
  type        = string
}

variable "private_subnet_ids" {
  description = "私有子網路 IDs，用於 Vault 的內部服務"
  type        = list(string)
}

variable "namespace" {
  description = "Vault 所在的 Kubernetes 命名空間"
  type        = string
  default     = "vault"
}

variable "chart_version" {
  description = "Vault Helm Chart 版本"
  type        = string
  default     = "0.25.0" # 請根據最新版本更新
}

variable "replicas" {
  description = "Vault 伺服器的副本數量"
  type        = number
  default     = 3 # HA 模式通常使用 3 個副本
}

variable "eks_node_role_arns" {
  description = "EKS 節點角色的 ARN 列表，用於設置 KMS 金鑰存取權限"
  type        = list(string)
  default     = []
}

variable "tags" {
  description = "要套用到資源的標籤"
  type        = map(string)
  default     = {}
}

variable "aws_load_balancer_controller_enabled" {
  description = "是否啟用 AWS Load Balancer Controller"
  type        = bool
  default     = true
}

variable "aws_load_balancer_controller_dependency" {
  description = "AWS Load Balancer Controller 的依賴項"
  type        = any
  default     = null
}
