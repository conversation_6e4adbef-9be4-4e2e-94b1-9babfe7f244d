locals {
  service_account_name = "cluster-autoscaler"
  namespace            = "kube-system"
}

# 使用 IRSA 模組創建 IAM 角色供 Cluster Autoscaler 使用
module "irsa_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "~> 5.30.0"

  role_name        = "${var.cluster_name}-cluster-autoscaler"
  role_description = "IAM role for Cluster Autoscaler"
  role_policy_arns = { policy = aws_iam_policy.this.arn }

  oidc_providers = {
    eks = {
      provider_arn               = var.oidc_provider_arn
      namespace_service_accounts = ["${local.namespace}:${local.service_account_name}"]
    }
  }

  tags = var.tags
}

# 創建 IAM 政策
resource "aws_iam_policy" "this" {
  name        = "${var.cluster_name}-cluster-autoscaler"
  path        = "/"
  description = "IAM policy for Cluster Autoscaler"
  policy      = file("${path.module}/iam-policy.json")

  tags = var.tags
}

# 政策已透過 IRSA 模組附加到角色，不需要單獨的 policy_attachment

# 安裝 Cluster Autoscaler Helm Chart
resource "helm_release" "cluster_autoscaler" {
  name       = "cluster-autoscaler"
  repository = "https://kubernetes.github.io/autoscaler"
  chart      = "cluster-autoscaler"
  version    = var.chart_version
  namespace  = local.namespace

  set {
    name  = "rbac.serviceAccount.name"
    value = local.service_account_name
  }

  set {
    name  = "rbac.serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = module.irsa_role.iam_role_arn
    type  = "string"
  }

  set {
    name  = "autoDiscovery.clusterName"
    value = var.cluster_name
  }

  set {
    name  = "awsRegion"
    value = var.region
  }

  set {
    name  = "rbac.create"
    value = "true"
  }

  set {
    name  = "cloudProvider"
    value = "aws"
  }

  # 啟用自動發現
  set {
    name  = "autoDiscovery.enabled"
    value = "true"
  }

  # 設置資源請求和限制
  set {
    name  = "resources.limits.cpu"
    value = "100m"
  }

  set {
    name  = "resources.limits.memory"
    value = "300Mi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "resources.requests.memory"
    value = "300Mi"
  }

  depends_on = [
    module.irsa_role
  ]
}
