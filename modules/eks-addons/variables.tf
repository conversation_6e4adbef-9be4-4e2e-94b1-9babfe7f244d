variable "cluster_name" {
  description = "EKS 叢集名稱"
  type        = string
}

variable "oidc_provider_arn" {
  description = "EKS OIDC 提供者的 ARN"
  type        = string
}

variable "oidc_provider_url" {
  description = "EKS OIDC 提供者的 URL"
  type        = string
  default     = ""
}

variable "region" {
  description = "AWS 區域"
  type        = string
}

variable "cluster_endpoint" {
  description = "EKS 另集端點 URL"
  type        = string
}

variable "cluster_ca_certificate" {
  description = "EKS 另集 CA 認證資料 (base64 編碼)"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID"
  type        = string
}

variable "tags" {
  description = "資源標籤"
  type        = map(string)
  default     = {}
}

# AWS Load Balancer Controller 變數
variable "enable_aws_load_balancer_controller" {
  description = "是否啟用 AWS Load Balancer Controller"
  type        = bool
  default     = true
}

variable "aws_load_balancer_controller_chart_version" {
  description = "AWS Load Balancer Controller Helm Chart 版本"
  type        = string
  default     = "1.6.2"
}

# Cluster Autoscaler 變數
variable "enable_cluster_autoscaler" {
  description = "是否啟用 Cluster Autoscaler"
  type        = bool
  default     = true
}

variable "cluster_autoscaler_chart_version" {
  description = "Cluster Autoscaler Helm Chart 版本"
  type        = string
  default     = "9.29.0"
}

# External DNS 變數
variable "enable_external_dns" {
  description = "是否啟用 External DNS"
  type        = bool
  default     = true
}

variable "external_dns_chart_version" {
  description = "External DNS Helm Chart 版本"
  type        = string
  default     = "1.13.1" # 最新穩定版本
}

variable "external_dns_create_namespace" {
  description = "是否建立 External DNS 命名空間"
  type        = bool
  default     = true
}

variable "external_dns_namespace" {
  description = "External DNS 命名空間"
  type        = string
  default     = "external-dns"
}

variable "external_dns_service_account_name" {
  description = "External DNS 服務帳號名稱"
  type        = string
  default     = "external-dns"
}

variable "external_dns_domain_filters" {
  description = "限制哪些域名會被同步 (e.g. ['example.com'])"
  type        = list(string)
  default     = []
}

variable "external_dns_policy" {
  description = "告訴 External DNS 應如何同步 DNS 記錄 (sync, upsert-only)"
  type        = string
  default     = "upsert-only" # 安全的選擇，避免刪除現有記錄
}

variable "external_dns_hosted_zone_arns" {
  description = "External DNS 可以管理的 Route53 託管區域 ARN 列表"
  type        = list(string)
  default     = ["arn:aws:route53:::hostedzone/*"]
}

variable "external_dns_zone_type" {
  description = "Route53 區域類型 (public, private)"
  type        = string
  default     = "public"
}

variable "external_dns_txt_owner_id" {
  description = "TXT 記錄擁有者識別碼，用於識別由 External DNS 創建的記錄"
  type        = string
  default     = "" # 預設將使用叢集名稱
}

variable "external_dns_txt_prefix" {
  description = "TXT 記錄前綴，用於避免 DNS 名稱衝突問題"
  type        = string
  default     = "cname-" # 預設使用 cname- 作為前綴
}

# External Secrets 變數
variable "enable_external_secrets" {
  description = "是否啟用 External Secrets"
  type        = bool
  default     = true
}

variable "external_secrets_chart_version" {
  description = "External Secrets Helm Chart 版本"
  type        = string
  default     = "0.9.9"
}

variable "external_secrets_create_namespace" {
  description = "是否建立 External Secrets 命名空間"
  type        = bool
  default     = true
}

variable "external_secrets_namespace" {
  description = "External Secrets 命名空間"
  type        = string
  default     = "external-secrets"
}

# Metrics Server 變數
variable "enable_metrics_server" {
  description = "是否啟用 Metrics Server"
  type        = bool
  default     = true
}

variable "metrics_server_addon_version" {
  description = "Metrics Server EKS Addon 版本"
  type        = string
  default     = "v0.7.2-eksbuild.2" # AWS EKS Addon 對應版本
}

variable "metrics_server_create_namespace" {
  description = "是否建立 Metrics Server 命名空間"
  type        = bool
  default     = false
}

variable "metrics_server_namespace" {
  description = "Metrics Server 命名空間"
  type        = string
  default     = "kube-system"
}

# Reloader 變數
variable "enable_reloader" {
  description = "是否啟用 Reloader"
  type        = bool
  default     = true
}

variable "reloader_chart_version" {
  description = "Reloader Helm Chart 版本"
  type        = string
  default     = "1.0.55"
}

variable "reloader_create_namespace" {
  description = "是否建立 Reloader 命名空間"
  type        = bool
  default     = false
}

variable "reloader_namespace" {
  description = "Reloader 命名空間"
  type        = string
  default     = "kube-system"
}

# EBS CSI Driver 變數
variable "enable_ebs_csi_driver" {
  description = "是否啟用 EBS CSI Driver"
  type        = bool
  default     = true
}

variable "ebs_csi_driver_addon_version" {
  description = "EBS CSI Driver EKS 附加元件版本"
  type        = string
  default     = null # 預設使用最新推薦版本
}

#######################
# kube-proxy
#######################
variable "enable_kube_proxy" {
  description = "是否啟用 kube-proxy"
  type        = bool
  default     = true
}

variable "kube_proxy_addon_version" {
  description = "kube-proxy 附加元件版本"
  type        = string
  default     = "v1.33.0-eksbuild.2"
}

#######################
# Amazon VPC CNI
#######################
variable "enable_vpc_cni" {
  description = "是否啟用 VPC CNI"
  type        = bool
  default     = false
}

variable "vpc_cni_addon_version" {
  description = "VPC CNI EKS Addon 版本"
  type        = string
  default     = "v1.13.8-eksbuild.1" # AWS EKS Addon 對應版本
}

# CoreDNS 變數
variable "enable_coredns" {
  description = "是否啟用 CoreDNS"
  type        = bool
  default     = false
}

variable "coredns_addon_version" {
  description = "CoreDNS EKS Addon 版本"
  type        = string
  default     = "v1.12.1-eksbuild.2" # AWS EKS Addon 對應版本
}

#######################
# HashiCorp Vault
#######################
variable "enable_vault" {
  description = "是否啟用 HashiCorp Vault"
  type        = bool
  default     = true
}

variable "private_subnet_ids" {
  description = "私有子網路 IDs，用於 Vault 的內部服務"
  type        = list(string)
  default     = [] # 預設為空，由調用模組提供
}

variable "vault_namespace" {
  description = "Vault 安裝的 Kubernetes 命名空間"
  type        = string
  default     = "vault"
}

variable "vault_release_name" {
  description = "Vault Helm Release 名稱"
  type        = string
  default     = "vault"
}

variable "vault_chart_version" {
  description = "Vault Helm Chart 版本"
  type        = string
  default     = "0.27.0"
}

variable "vault_values_yaml" {
  description = "自定義 Vault 設定的 YAML 字串"
  type        = string
  default     = ""
}

variable "vault_ha_enabled" {
  description = "是否啟用 Vault 高可用模式"
  type        = bool
  default     = false
}

variable "vault_replicas" {
  description = "Vault 伺服器副本數量"
  type        = number
  default     = 3 # 高可用性配置，確保 PDB 能正常運作
}

variable "eks_node_role_arns" {
  description = "EKS 節點群組的 IAM 角色 ARN 列表，用於配置 Vault KMS 金鑰權限"
  type        = list(string)
  default     = []
}

variable "vault_additional_set_values" {
  description = "Vault 額外的 Helm 設定值"
  type        = list(map(string))
  default     = []
}

variable "vault_awskms_key_id" {
  description = "用於 Vault 自動解封的 AWS KMS 金鑰 ID"
  type        = string
  default     = ""
}

variable "vault_create_namespace" {
  description = "是否為 Vault 創建命名空間"
  type        = bool
  default     = true
}

variable "vault_certificate_arn" {
  description = "Vault 使用的 ACM 資源 ARN"
  type        = string
  default     = ""
}

variable "eks_oidc_provider_url" {
  description = "EKS OIDC 提供者 URL"
  type        = string
  default     = ""
}

variable "cluster_oidc_issuer_url" {
  description = "EKS 叉集的 OIDC 發行者 URL"
  type        = string
  default     = ""
}

# ALB 相關設定已移除
