variable "aws_region" {
  description = "AWS 區域"
  type        = string
  default     = "ap-east-2"
}

variable "domain_name" {
  description = "要申請 SSL/TLS 憑證的主要網域名稱"
  type        = string
}

variable "subject_alternative_names" {
  description = "憑證的主體備用名稱 (SAN)"
  type        = list(string)
  default     = []
}

variable "route53_zone_id" {
  description = "Route53 託管區域的 ID"
  type        = string
}

variable "tags" {
  description = "要套用到 ACM 憑證的標籤"
  type        = map(string)
  default     = {}
}
