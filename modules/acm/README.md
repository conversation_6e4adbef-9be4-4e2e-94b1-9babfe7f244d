# ACM 模組

## 概覽

此模組創建和管理 AWS Certificate Manager (ACM) SSL/TLS 憑證，支援自動 DNS 驗證和多域名憑證。適用於 HTTPS 網站、API 和負載均衡器的 SSL 憑證需求。

## 功能特性

- ✅ **SSL/TLS 憑證**：自動申請和管理 SSL/TLS 憑證
- ✅ **DNS 驗證**：使用 Route53 進行自動 DNS 驗證
- ✅ **多域名支援**：支援主域名和 SAN (Subject Alternative Names)
- ✅ **自動續期**：AWS 自動處理憑證續期
- ✅ **Route53 整合**：自動創建 DNS 驗證記錄
- ✅ **標籤管理**：完整的資源標籤支援
- ✅ **生命週期管理**：安全的憑證替換策略

## 架構圖

```mermaid
graph TB
    subgraph "AWS Certificate Manager"
        CERT[SSL/TLS 憑證<br/>*.example.com]
        VALIDATION[憑證驗證]
    end
    
    subgraph "Route53"
        ZONE[託管區域<br/>example.com]
        RECORD[DNS 驗證記錄<br/>_acme-challenge]
    end
    
    subgraph "應用服務"
        ALB[Application Load Balancer]
        CF[CloudFront]
        API[API Gateway]
    end
    
    subgraph "用戶端"
        BROWSER[瀏覽器]
        APP[應用程式]
    end
    
    CERT --> VALIDATION
    VALIDATION --> RECORD
    RECORD --> ZONE
    
    CERT --> ALB
    CERT --> CF
    CERT --> API
    
    BROWSER --> ALB
    APP --> API
    
    ALB -.-> CERT
    CF -.-> CERT
    API -.-> CERT
```

## 使用方式

### 基本使用（單域名）

```hcl
module "acm_cert" {
  source = "./modules/acm"

  domain_name      = "example.com"
  route53_zone_id  = "Z1234567890ABC"
  
  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### 多域名配置

```hcl
module "acm_cert" {
  source = "./modules/acm"

  domain_name = "example.com"
  subject_alternative_names = [
    "*.example.com",
    "api.example.com",
    "www.example.com"
  ]
  
  route53_zone_id = "Z1234567890ABC"
  aws_region      = "ap-east-2"
  
  tags = {
    Environment        = "prod"
    Project           = "production-app"
    CostCenter        = "engineering"
    DataClassification = "public"
  }
}
```

### 通配符憑證

```hcl
module "wildcard_cert" {
  source = "./modules/acm"

  domain_name = "*.example.com"
  subject_alternative_names = [
    "example.com"  # 包含根域名
  ]
  
  route53_zone_id = data.aws_route53_zone.main.zone_id
  
  tags = {
    Environment = "prod"
    Project     = "wildcard-ssl"
    Purpose     = "multi-subdomain"
  }
}
```

## 輸入變數

| 名稱 | 描述 | 類型 | 預設值 | 必要 |
|------|------|------|--------|------|
| `domain_name` | 要申請 SSL/TLS 憑證的主要域名 | `string` | - | ✅ |
| `route53_zone_id` | Route53 託管區域的 ID | `string` | - | ✅ |
| `subject_alternative_names` | 憑證的主體備用名稱 (SAN) | `list(string)` | `[]` | ❌ |
| `aws_region` | AWS 區域 | `string` | `"ap-east-2"` | ❌ |
| `tags` | 要套用到 ACM 憑證的標籤 | `map(string)` | `{}` | ❌ |

## 輸出值

| 名稱 | 描述 |
|------|------|
| `certificate_arn` | ACM 憑證的 ARN |
| `certificate_domain_validation_options` | 域名驗證選項 |
| `certificate_status` | 憑證狀態 |

## 憑證類型範例

### 1. 單域名憑證
```hcl
domain_name = "api.example.com"
subject_alternative_names = []
# 結果：僅保護 api.example.com
```

### 2. 多域名憑證
```hcl
domain_name = "example.com"
subject_alternative_names = [
  "www.example.com",
  "api.example.com",
  "admin.example.com"
]
# 結果：保護 4 個域名
```

### 3. 通配符憑證
```hcl
domain_name = "*.example.com"
subject_alternative_names = ["example.com"]
# 結果：保護所有子域名和根域名
```

### 4. 混合憑證
```hcl
domain_name = "example.com"
subject_alternative_names = [
  "*.example.com",
  "*.api.example.com"
]
# 結果：保護根域名、所有一級子域名和 api 的二級子域名
```

## DNS 驗證流程

### 自動驗證過程
1. **申請憑證**：向 ACM 提交憑證申請
2. **生成驗證記錄**：ACM 生成 DNS 驗證記錄
3. **創建 DNS 記錄**：自動在 Route53 中創建驗證記錄
4. **等待驗證**：AWS 驗證 DNS 記錄
5. **頒發憑證**：驗證成功後頒發憑證

### 驗證記錄格式
```
名稱: _acme-challenge.example.com
類型: CNAME
值: _validation-hash.acm-validations.aws.
```

## 最佳實踐

### 1. 域名規劃
```hcl
# 建議的域名結構
domain_name = "example.com"
subject_alternative_names = [
  "*.example.com",      # 所有子域名
  "example.com"         # 根域名（如果主域名是通配符）
]
```

### 2. 區域考量
- 在 `us-east-1` 申請用於 CloudFront 的憑證
- 在應用程式所在區域申請用於 ALB 的憑證
- 考慮跨區域憑證複製需求

### 3. 生命週期管理
```hcl
lifecycle {
  create_before_destroy = true
}
```

### 4. 標籤策略
```hcl
tags = {
  Environment = "prod"
  Project     = "web-app"
  Domain      = "example.com"
  Purpose     = "https-termination"
  Owner       = "platform-team"
}
```

## 使用場景

### 1. Application Load Balancer
```hcl
resource "aws_lb_listener" "https" {
  load_balancer_arn = aws_lb.main.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  certificate_arn   = module.acm_cert.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.main.arn
  }
}
```

### 2. CloudFront 分發
```hcl
resource "aws_cloudfront_distribution" "main" {
  # ... 其他配置

  viewer_certificate {
    acm_certificate_arn      = module.acm_cert.certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }
}
```

### 3. API Gateway
```hcl
resource "aws_api_gateway_domain_name" "main" {
  domain_name     = "api.example.com"
  certificate_arn = module.acm_cert.certificate_arn
}
```

## 故障排除

### 常見問題

#### 1. DNS 驗證失敗
**問題**：憑證驗證停留在 "Pending validation" 狀態

**解決方案**：
```bash
# 檢查 DNS 記錄
dig _acme-challenge.example.com CNAME

# 檢查 Route53 記錄
aws route53 list-resource-record-sets --hosted-zone-id Z1234567890ABC
```

#### 2. 權限不足
**問題**：無法創建 DNS 驗證記錄
```
Error: AccessDenied: User is not authorized to perform: route53:ChangeResourceRecordSets
```

**解決方案**：
- 檢查 IAM 權限
- 確認 Route53 託管區域權限

#### 3. 域名不匹配
**問題**：域名與託管區域不匹配

**解決方案**：
```bash
# 檢查託管區域
aws route53 get-hosted-zone --id Z1234567890ABC

# 確認域名所有權
whois example.com
```

### 調試指令

```bash
# 檢查憑證狀態
aws acm describe-certificate --certificate-arn <certificate-arn>

# 檢查域名驗證選項
aws acm describe-certificate --certificate-arn <certificate-arn> \
  --query 'Certificate.DomainValidationOptions'

# 檢查 Route53 記錄
aws route53 list-resource-record-sets --hosted-zone-id <zone-id> \
  --query 'ResourceRecordSets[?Type==`CNAME`]'
```

## 成本考量

### ACM 定價
- **公有憑證**：免費（由 AWS 管理的域名驗證憑證）
- **私有憑證**：需要 AWS Private CA（額外費用）
- **DNS 查詢**：Route53 DNS 查詢費用

### 成本優化
1. 使用通配符憑證減少憑證數量
2. 合理規劃 SAN 域名
3. 避免不必要的私有憑證

## 安全考量

### 1. 憑證管理
- 定期檢查憑證到期時間
- 監控憑證狀態變化
- 實施憑證輪換策略

### 2. DNS 安全
- 保護 Route53 託管區域
- 監控 DNS 記錄變更
- 實施 DNS 存取控制

### 3. 傳輸安全
- 使用最新的 TLS 版本
- 配置安全的密碼套件
- 實施 HSTS 標頭

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0

## 相關資源

- [AWS Certificate Manager 用戶指南](https://docs.aws.amazon.com/acm/latest/userguide/)
- [Route53 開發者指南](https://docs.aws.amazon.com/route53/latest/developerguide/)
- [SSL/TLS 最佳實踐](https://docs.aws.amazon.com/acm/latest/userguide/acm-bestpractices.html)
- [DNS 驗證故障排除](https://docs.aws.amazon.com/acm/latest/userguide/troubleshooting-DNS-validation.html)

## 範例

查看 [examples](../../examples/) 目錄以獲取更多使用範例。
