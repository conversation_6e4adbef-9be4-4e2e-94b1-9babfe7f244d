# Secrets 模組

## 概覽

此模組創建和管理 AWS Secrets Manager 密碼，用於安全地儲存和管理敏感資訊，如資料庫密碼、API 金鑰和其他機密資料。支援自動輪換、版本控制和細粒度存取控制。

## 功能特性

- ✅ **安全儲存**：加密儲存敏感資訊
- ✅ **版本控制**：自動管理密碼版本
- ✅ **存取控制**：IAM 整合的細粒度權限
- ✅ **自動輪換**：支援自動密碼輪換（可選）
- ✅ **跨服務整合**：與 RDS、EKS 等服務整合
- ✅ **審計追蹤**：完整的存取日誌記錄
- ✅ **標籤管理**：完整的資源標籤支援
- ✅ **災難恢復**：可配置的恢復等待期

## 架構圖

```mermaid
graph TB
    subgraph "AWS Secrets Manager"
        SECRET[密碼<br/>database-password]
        VERSION[密碼版本]
        ENCRYPTION[KMS 加密]
    end
    
    subgraph "應用程式"
        APP1[Web 應用程式]
        APP2[API 服務]
        APP3[批次作業]
    end
    
    subgraph "資料庫"
        RDS[Aurora PostgreSQL]
    end
    
    subgraph "Kubernetes"
        POD1[應用程式 Pod]
        POD2[作業 Pod]
        ESO[External Secrets Operator]
    end
    
    subgraph "IAM"
        ROLE1[應用程式角色]
        ROLE2[EKS 服務帳號角色]
        POLICY[Secrets 存取政策]
    end
    
    APP1 --> ROLE1
    APP2 --> ROLE1
    APP3 --> ROLE1
    
    POD1 --> ESO
    POD2 --> ESO
    ESO --> ROLE2
    
    ROLE1 --> POLICY
    ROLE2 --> POLICY
    POLICY --> SECRET
    
    SECRET --> VERSION
    SECRET --> ENCRYPTION
    
    APP1 -.-> RDS
    APP2 -.-> RDS
    POD1 -.-> RDS
```

## 使用方式

### 基本使用

```hcl
module "db_secrets" {
  source = "./modules/secrets"

  identifier  = "my-database"
  db_password = random_password.db_password.result
  
  tags = {
    Environment = "dev"
    Project     = "my-project"
    Purpose     = "database-auth"
  }
}

# 生成隨機密碼
resource "random_password" "db_password" {
  length  = 16
  special = true
}
```

### 進階配置

```hcl
module "app_secrets" {
  source = "./modules/secrets"

  identifier  = "production-app-db"
  db_password = random_password.secure_password.result
  
  tags = {
    Environment        = "prod"
    Project           = "production-app"
    CostCenter        = "engineering"
    DataClassification = "confidential"
    Purpose           = "database-credentials"
    Owner             = "platform-team"
  }
}

# 生成強密碼
resource "random_password" "secure_password" {
  length  = 32
  special = true
  upper   = true
  lower   = true
  numeric = true
  
  # 確保包含各種字符類型
  min_upper   = 2
  min_lower   = 2
  min_numeric = 2
  min_special = 2
  
  # 避免混淆字符
  override_special = "!#$%&*()-_=+[]{}<>:?"
}
```

## 輸入變數

| 名稱 | 描述 | 類型 | 預設值 | 必要 |
|------|------|------|--------|------|
| `identifier` | 密碼識別碼前綴 | `string` | - | ✅ |
| `db_password` | 資料庫密碼 | `string` | - | ✅ |
| `tags` | 資源標籤 | `map(string)` | `{}` | ❌ |

## 輸出值

| 名稱 | 描述 |
|------|------|
| `secret_arn` | Secrets Manager 密碼 ARN |
| `secret_id` | Secrets Manager 密碼 ID |
| `secret_name` | Secrets Manager 密碼名稱 |

## 密碼格式

此模組儲存的密碼採用 JSON 格式，包含完整的資料庫連接資訊：

```json
{
  "username": "postgres",
  "password": "generated-secure-password",
  "engine": "postgres",
  "host": "database-endpoint.region.rds.amazonaws.com",
  "port": 5432,
  "dbname": "database-name"
}
```

## 存取密碼

### 1. AWS CLI
```bash
# 獲取密碼值
aws secretsmanager get-secret-value \
  --secret-id "my-database-password" \
  --query SecretString --output text

# 解析 JSON 格式密碼
aws secretsmanager get-secret-value \
  --secret-id "my-database-password" \
  --query SecretString --output text | jq -r '.password'
```

### 2. Python (boto3)
```python
import boto3
import json

client = boto3.client('secretsmanager')

def get_secret(secret_name):
    try:
        response = client.get_secret_value(SecretId=secret_name)
        secret = json.loads(response['SecretString'])
        return secret
    except Exception as e:
        print(f"Error retrieving secret: {e}")
        return None

# 使用範例
secret = get_secret('my-database-password')
if secret:
    password = secret['password']
    username = secret['username']
```

### 3. Node.js (AWS SDK)
```javascript
const AWS = require('aws-sdk');
const secretsManager = new AWS.SecretsManager();

async function getSecret(secretName) {
  try {
    const data = await secretsManager.getSecretValue({
      SecretId: secretName
    }).promise();
    
    return JSON.parse(data.SecretString);
  } catch (error) {
    console.error('Error retrieving secret:', error);
    throw error;
  }
}

// 使用範例
const secret = await getSecret('my-database-password');
const password = secret.password;
```

### 4. Kubernetes (External Secrets Operator)
```yaml
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: aws-secrets-manager
spec:
  provider:
    aws:
      service: SecretsManager
      region: ap-east-2
      auth:
        serviceAccount:
          name: external-secrets-sa

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: database-secret
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-manager
    kind: SecretStore
  target:
    name: database-credentials
    creationPolicy: Owner
  data:
  - secretKey: password
    remoteRef:
      key: my-database-password
      property: password
  - secretKey: username
    remoteRef:
      key: my-database-password
      property: username
```

## IAM 權限

### 讀取權限政策
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue",
        "secretsmanager:DescribeSecret"
      ],
      "Resource": "arn:aws:secretsmanager:region:account:secret:secret-name-*"
    }
  ]
}
```

### 管理權限政策
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue",
        "secretsmanager:DescribeSecret",
        "secretsmanager:PutSecretValue",
        "secretsmanager:UpdateSecret",
        "secretsmanager:RotateSecret"
      ],
      "Resource": "arn:aws:secretsmanager:region:account:secret:secret-name-*"
    }
  ]
}
```

## 最佳實踐

### 1. 密碼生成
```hcl
# 強密碼生成
resource "random_password" "secure" {
  length  = 32
  special = true
  upper   = true
  lower   = true
  numeric = true
  
  # 確保字符多樣性
  min_upper   = 3
  min_lower   = 3
  min_numeric = 3
  min_special = 3
  
  # 避免混淆字符
  override_special = "!@#$%^&*()_+-=[]{}|;:,.<>?"
}
```

### 2. 存取控制
- 使用最小權限原則
- 為不同環境使用不同的 IAM 角色
- 定期審查存取權限
- 啟用 CloudTrail 記錄

### 3. 輪換策略
```hcl
# 啟用自動輪換（可選）
resource "aws_secretsmanager_secret_rotation" "example" {
  secret_id           = module.secrets.secret_id
  rotation_lambda_arn = aws_lambda_function.rotation.arn
  
  rotation_rules {
    automatically_after_days = 30
  }
}
```

### 4. 標籤策略
```hcl
tags = {
  Environment        = "prod"
  Project           = "app-name"
  DataClassification = "confidential"
  Purpose           = "database-credentials"
  Owner             = "platform-team"
  RotationEnabled   = "false"
}
```

## 故障排除

### 常見問題

#### 1. 存取被拒絕
**問題**：應用程式無法讀取密碼
```
Error: AccessDenied: User is not authorized to perform: secretsmanager:GetSecretValue
```

**解決方案**：
- 檢查 IAM 角色權限
- 確認密碼 ARN 正確
- 驗證資源政策

#### 2. 密碼不存在
**問題**：找不到指定的密碼
```
Error: ResourceNotFoundException: Secrets Manager can't find the specified secret
```

**解決方案**：
```bash
# 列出所有密碼
aws secretsmanager list-secrets

# 檢查密碼狀態
aws secretsmanager describe-secret --secret-id <secret-name>
```

#### 3. 解析錯誤
**問題**：無法解析密碼 JSON 格式

**解決方案**：
```bash
# 檢查密碼格式
aws secretsmanager get-secret-value --secret-id <secret-name> \
  --query SecretString --output text | jq .
```

### 調試指令

```bash
# 檢查密碼詳情
aws secretsmanager describe-secret --secret-id <secret-name>

# 獲取密碼值
aws secretsmanager get-secret-value --secret-id <secret-name>

# 檢查密碼版本
aws secretsmanager list-secret-version-ids --secret-id <secret-name>

# 檢查 IAM 權限
aws iam simulate-principal-policy \
  --policy-source-arn <role-arn> \
  --action-names secretsmanager:GetSecretValue \
  --resource-arns <secret-arn>
```

## 成本考量

### Secrets Manager 定價
- **密碼儲存**：每個密碼 $0.40/月
- **API 調用**：每 10,000 次請求 $0.05
- **輪換**：每次輪換額外費用

### 成本優化建議
1. 合併相關密碼到單一 JSON 物件
2. 實施適當的快取策略
3. 避免不必要的 API 調用
4. 定期清理未使用的密碼

## 安全考量

### 1. 加密
- 使用 AWS KMS 進行靜態加密
- 傳輸中使用 TLS 加密
- 定期輪換加密金鑰

### 2. 存取控制
- 實施最小權限原則
- 使用 IAM 角色而非用戶
- 定期審查存取權限

### 3. 監控和審計
- 啟用 CloudTrail 記錄
- 監控異常存取模式
- 設置存取警報

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0

## 相關資源

- [AWS Secrets Manager 用戶指南](https://docs.aws.amazon.com/secretsmanager/latest/userguide/)
- [Secrets Manager 最佳實踐](https://docs.aws.amazon.com/secretsmanager/latest/userguide/best-practices.html)
- [External Secrets Operator](https://external-secrets.io/)
- [密碼輪換指南](https://docs.aws.amazon.com/secretsmanager/latest/userguide/rotating-secrets.html)

## 範例

查看 [examples](../../examples/) 目錄以獲取更多使用範例。
