# Route53 模組

## 概覽

此模組創建和管理 AWS Route53 託管區域和 DNS 記錄，提供可靠的 DNS 服務。支援多種記錄類型、健康檢查和流量路由策略，適用於網域名稱解析和流量管理。

## 功能特性

- ✅ **託管區域**：創建和管理 DNS 託管區域
- ✅ **多記錄類型**：支援 A、AAAA、CNAME、MX、TXT 等記錄
- ✅ **別名記錄**：支援 AWS 資源的別名記錄
- ✅ **健康檢查**：DNS 故障轉移和健康監控
- ✅ **流量路由**：支援加權、延遲和地理位置路由
- ✅ **DNSSEC**：支援 DNS 安全擴展（可選）
- ✅ **標籤管理**：完整的資源標籤支援
- ✅ **成本優化**：高效的 DNS 查詢處理

## 架構圖

```mermaid
graph TB
    subgraph "Route53"
        subgraph "託管區域"
            ZONE[example.com<br/>託管區域]
        end
        
        subgraph "DNS 記錄"
            A_RECORD[A 記錄<br/>example.com → IP]
            CNAME[CNAME 記錄<br/>www → example.com]
            ALIAS[別名記錄<br/>api → ALB]
            MX[MX 記錄<br/>郵件伺服器]
        end
        
        subgraph "健康檢查"
            HC1[健康檢查 1]
            HC2[健康檢查 2]
        end
    end
    
    subgraph "AWS 資源"
        ALB[Application Load Balancer]
        CF[CloudFront 分發]
        S3[S3 靜態網站]
    end
    
    subgraph "外部資源"
        SERVER1[Web 伺服器 1]
        SERVER2[Web 伺服器 2]
        MAIL[郵件伺服器]
    end
    
    subgraph "用戶端"
        USER[使用者]
        BROWSER[瀏覽器]
    end
    
    USER --> ZONE
    BROWSER --> ZONE
    
    ZONE --> A_RECORD
    ZONE --> CNAME
    ZONE --> ALIAS
    ZONE --> MX
    
    A_RECORD --> SERVER1
    CNAME --> A_RECORD
    ALIAS --> ALB
    MX --> MAIL
    
    ALB --> CF
    ALB --> S3
    
    HC1 --> SERVER1
    HC2 --> SERVER2
```

## 使用方式

### 基本使用

```hcl
module "route53" {
  source = "./modules/route53"

  domain_name = "example.com"
  
  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### 完整配置

```hcl
module "route53" {
  source = "./modules/route53"

  domain_name = "example.com"
  
  # DNS 記錄配置
  dns_records = {
    # A 記錄
    "www" = {
      type    = "A"
      ttl     = 300
      records = ["***********", "***********"]
    }
    
    # CNAME 記錄
    "blog" = {
      type    = "CNAME"
      ttl     = 300
      records = ["www.example.com"]
    }
    
    # MX 記錄
    "" = {
      type    = "MX"
      ttl     = 300
      records = ["10 mail.example.com"]
    }
    
    # TXT 記錄（SPF）
    "" = {
      type    = "TXT"
      ttl     = 300
      records = ["v=spf1 include:_spf.google.com ~all"]
    }
  }
  
  # 別名記錄配置
  alias_records = {
    # ALB 別名
    "api" = {
      zone_id = data.aws_lb.api.zone_id
      name    = data.aws_lb.api.dns_name
    }
    
    # CloudFront 別名
    "" = {
      zone_id = data.aws_cloudfront_distribution.main.hosted_zone_id
      name    = data.aws_cloudfront_distribution.main.domain_name
    }
  }
  
  tags = {
    Environment        = "prod"
    Project           = "production-app"
    CostCenter        = "engineering"
    DataClassification = "public"
  }
}
```

## 輸入變數

| 名稱 | 描述 | 類型 | 預設值 | 必要 |
|------|------|------|--------|------|
| `domain_name` | 要創建託管區域的域名 | `string` | - | ✅ |
| `dns_records` | DNS 記錄配置 | `map(object)` | `{}` | ❌ |
| `alias_records` | 別名記錄配置 | `map(object)` | `{}` | ❌ |
| `tags` | 資源標籤 | `map(string)` | `{}` | ❌ |

### DNS 記錄結構
```hcl
dns_records = {
  "subdomain" = {
    type    = "A"           # 記錄類型
    ttl     = 300           # TTL（秒）
    records = ["*******"]   # 記錄值列表
  }
}
```

### 別名記錄結構
```hcl
alias_records = {
  "subdomain" = {
    zone_id = "Z123456789"     # 目標資源的託管區域 ID
    name    = "example.com"    # 目標資源的 DNS 名稱
  }
}
```

## 輸出值

| 名稱 | 描述 |
|------|------|
| `zone_id` | Route53 託管區域 ID |
| `zone_arn` | Route53 託管區域 ARN |
| `name_servers` | 託管區域的名稱伺服器列表 |
| `zone_name` | 託管區域名稱 |

## 記錄類型範例

### 1. A 記錄（IPv4 地址）
```hcl
"www" = {
  type    = "A"
  ttl     = 300
  records = ["***********"]
}
```

### 2. AAAA 記錄（IPv6 地址）
```hcl
"www" = {
  type    = "AAAA"
  ttl     = 300
  records = ["2001:db8::1"]
}
```

### 3. CNAME 記錄
```hcl
"blog" = {
  type    = "CNAME"
  ttl     = 300
  records = ["www.example.com"]
}
```

### 4. MX 記錄（郵件）
```hcl
"" = {  # 根域名
  type    = "MX"
  ttl     = 300
  records = [
    "10 mail1.example.com",
    "20 mail2.example.com"
  ]
}
```

### 5. TXT 記錄
```hcl
"" = {
  type    = "TXT"
  ttl     = 300
  records = [
    "v=spf1 include:_spf.google.com ~all",
    "google-site-verification=abc123"
  ]
}
```

## 別名記錄範例

### 1. Application Load Balancer
```hcl
"api" = {
  zone_id = data.aws_lb.api.zone_id
  name    = data.aws_lb.api.dns_name
}
```

### 2. CloudFront 分發
```hcl
"cdn" = {
  zone_id = data.aws_cloudfront_distribution.main.hosted_zone_id
  name    = data.aws_cloudfront_distribution.main.domain_name
}
```

### 3. S3 靜態網站
```hcl
"static" = {
  zone_id = data.aws_s3_bucket.website.hosted_zone_id
  name    = data.aws_s3_bucket.website.website_endpoint
}
```

## 最佳實踐

### 1. TTL 設定策略
```hcl
# 不同記錄類型的建議 TTL
dns_records = {
  # 靜態記錄 - 較長 TTL
  "www" = {
    type    = "A"
    ttl     = 3600  # 1 小時
    records = ["***********"]
  }
  
  # 動態記錄 - 較短 TTL
  "api" = {
    type    = "A"
    ttl     = 300   # 5 分鐘
    records = ["***********"]
  }
  
  # 測試記錄 - 很短 TTL
  "test" = {
    type    = "A"
    ttl     = 60    # 1 分鐘
    records = ["***********"]
  }
}
```

### 2. 安全配置
- 使用 DNSSEC（如果需要）
- 定期審查 DNS 記錄
- 監控 DNS 查詢模式
- 實施存取控制

### 3. 效能優化
- 使用別名記錄而非 CNAME（當可能時）
- 設定適當的 TTL 值
- 使用健康檢查進行故障轉移
- 考慮地理位置路由

### 4. 標籤策略
```hcl
tags = {
  Environment = "prod"
  Project     = "web-app"
  Domain      = "example.com"
  Purpose     = "dns-resolution"
  Owner       = "platform-team"
}
```

## 健康檢查和故障轉移

### 基本健康檢查
```hcl
resource "aws_route53_health_check" "main" {
  fqdn                            = "www.example.com"
  port                            = 80
  type                            = "HTTP"
  resource_path                   = "/health"
  failure_threshold               = 3
  request_interval                = 30

  tags = {
    Name = "www.example.com health check"
  }
}
```

### 故障轉移記錄
```hcl
# 主要記錄
resource "aws_route53_record" "primary" {
  zone_id = module.route53.zone_id
  name    = "www.example.com"
  type    = "A"
  
  set_identifier = "primary"
  failover_routing_policy {
    type = "PRIMARY"
  }
  
  health_check_id = aws_route53_health_check.main.id
  ttl             = 60
  records         = ["***********"]
}

# 備用記錄
resource "aws_route53_record" "secondary" {
  zone_id = module.route53.zone_id
  name    = "www.example.com"
  type    = "A"
  
  set_identifier = "secondary"
  failover_routing_policy {
    type = "SECONDARY"
  }
  
  ttl     = 60
  records = ["***********"]
}
```

## 故障排除

### 常見問題

#### 1. DNS 解析失敗
**問題**：域名無法解析

**解決方案**：
```bash
# 檢查 DNS 解析
dig example.com
nslookup example.com

# 檢查名稱伺服器
dig NS example.com
```

#### 2. TTL 過長導致變更延遲
**問題**：DNS 記錄更新後仍解析到舊值

**解決方案**：
- 等待 TTL 過期
- 使用較短的 TTL 進行測試
- 清除本地 DNS 快取

#### 3. 別名記錄配置錯誤
**問題**：別名記錄無法正常工作

**解決方案**：
```bash
# 檢查目標資源的 DNS 名稱
aws elbv2 describe-load-balancers --names my-alb

# 檢查託管區域 ID
aws elbv2 describe-load-balancers --names my-alb \
  --query 'LoadBalancers[0].CanonicalHostedZoneId'
```

### 調試指令

```bash
# 檢查託管區域
aws route53 list-hosted-zones

# 檢查 DNS 記錄
aws route53 list-resource-record-sets --hosted-zone-id Z123456789

# 檢查健康檢查
aws route53 list-health-checks

# 測試 DNS 解析
dig @******* example.com
```

## 成本考量

### Route53 定價
- **託管區域**：每個區域 $0.50/月
- **DNS 查詢**：每百萬次查詢 $0.40
- **健康檢查**：每個檢查 $0.50/月
- **流量路由**：每百萬次查詢額外費用

### 成本優化
1. 合併相關域名到單一託管區域
2. 使用適當的 TTL 減少查詢次數
3. 避免不必要的健康檢查
4. 定期清理未使用的記錄

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0

## 相關資源

- [Amazon Route53 開發者指南](https://docs.aws.amazon.com/route53/latest/developerguide/)
- [DNS 記錄類型參考](https://docs.aws.amazon.com/route53/latest/developerguide/ResourceRecordTypes.html)
- [Route53 最佳實踐](https://docs.aws.amazon.com/route53/latest/developerguide/best-practices-dns.html)
- [健康檢查和故障轉移](https://docs.aws.amazon.com/route53/latest/developerguide/dns-failover.html)

## 範例

查看 [examples](../../examples/) 目錄以獲取更多使用範例。
