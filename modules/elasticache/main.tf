# 子網路群組
resource "aws_elasticache_subnet_group" "this" {
  name       = "${var.identifier}-subnet-group"
  subnet_ids = var.subnet_ids

  tags = merge(
    {
      Name = "${var.identifier}-subnet-group"
    },
    var.tags
  )
}

# 安全群組
resource "aws_security_group" "this" {
  name        = "${var.identifier}-sg"
  description = "${var.identifier} ElastiCache security group"
  vpc_id      = var.vpc_id

  # 允許來自指定 CIDR 區塊的存取
  ingress {
    from_port   = var.port
    to_port     = var.port
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "Allow access from allowed CIDR blocks"
  }

  # 允許來自指定安全群組的存取
  dynamic "ingress" {
    for_each = var.security_group_ids
    content {
      from_port       = var.port
      to_port         = var.port
      protocol        = "tcp"
      security_groups = [ingress.value]
      description     = "Allow access from security group ${ingress.value}"
    }
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    {
      Name = "${var.identifier}-sg"
    },
    var.tags
  )
}

# Valkey/Redis 複製群組
resource "aws_elasticache_replication_group" "this" {
  replication_group_id = var.identifier
  description          = "${var.identifier} replication group"

  engine               = var.engine
  engine_version       = var.engine_version
  node_type            = var.node_type
  num_cache_clusters   = var.num_cache_nodes
  parameter_group_name = var.parameter_group_name
  port                 = var.port

  subnet_group_name  = aws_elasticache_subnet_group.this.name
  security_group_ids = [aws_security_group.this.id]

  # 叢集設定
  automatic_failover_enabled = var.num_cache_nodes > 1 ? true : false
  multi_az_enabled           = false

  # 加密設定
  at_rest_encryption_enabled = false
  transit_encryption_enabled = false

  tags = merge(
    {
      Name = var.identifier
    },
    var.tags
  )
}
