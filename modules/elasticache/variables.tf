variable "identifier" {
  description = "ElastiCache 叢集識別碼"
  type        = string
}

variable "engine" {
  description = "ElastiCache 引擎類型"
  type        = string
  default     = "valkey"
}

variable "node_type" {
  description = "ElastiCache 節點類型"
  type        = string
  default     = "cache.t4g.medium"
}

variable "num_cache_nodes" {
  description = "ElastiCache 節點數量"
  type        = number
  default     = 1
}

variable "engine_version" {
  description = "Valkey 引擎版本"
  type        = string
  default     = "7.2"
}

variable "parameter_group_name" {
  description = "參數群組名稱"
  type        = string
  default     = "default.valkey7"
}

variable "port" {
  description = "ElastiCache 連接埠"
  type        = number
  default     = 6379
}

variable "subnet_ids" {
  description = "子網路 ID 列表"
  type        = list(string)
}

variable "security_group_ids" {
  description = "安全群組 ID 列表"
  type        = list(string)
}

variable "vpc_id" {
  description = "VPC ID，用於建立安全群組"
  type        = string
}

variable "tags" {
  description = "資源標籤"
  type        = map(string)
  default     = {}
}

variable "allowed_cidr_blocks" {
  description = "允許存取的 CIDR 區塊列表"
  type        = list(string)
  default     = ["**********/16"]
}
