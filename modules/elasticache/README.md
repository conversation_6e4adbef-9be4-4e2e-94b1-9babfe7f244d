# ElastiCache 模組

## 概覽

此模組創建和管理 Amazon ElastiCache Valkey 叢集，提供高效能的記憶體內快取解決方案。Valkey 是 Redis 的開源分支，提供相同的 API 和功能，適用於快取、會話儲存和即時應用程式。

## 功能特性

- ✅ **Valkey 引擎**：使用最新的 Valkey 7.2 引擎
- ✅ **複製群組**：支援主從複製配置
- ✅ **自動故障轉移**：多節點時自動啟用故障轉移
- ✅ **子網路群組**：私有子網路部署
- ✅ **安全群組**：自動化網路安全配置
- ✅ **參數群組**：可自訂的快取參數
- ✅ **標籤管理**：完整的資源標籤支援
- ✅ **成本優化**：支援不同實例類型選擇

## 架構圖

```mermaid
graph TB
    subgraph "VPC"
        subgraph "私有子網路 A"
            CACHE1[Valkey 節點 1<br/>Primary]
        end
        
        subgraph "私有子網路 B"
            CACHE2[Valkey 節點 2<br/>Replica]
        end
        
        subgraph "私有子網路 C"
            CACHE3[Valkey 節點 3<br/>Replica]
        end
        
        subgraph "安全群組"
            SG[ElastiCache 安全群組<br/>Port 6379]
        end
        
        subgraph "子網路群組"
            SUBNET[快取子網路群組]
        end
    end
    
    subgraph "應用程式"
        APP1[應用程式 1]
        APP2[應用程式 2]
        APP3[應用程式 3]
    end
    
    APP1 --> SG
    APP2 --> SG
    APP3 --> SG
    
    SG --> CACHE1
    SG --> CACHE2
    SG --> CACHE3
    
    CACHE1 --> CACHE2
    CACHE1 --> CACHE3
    
    SUBNET --> CACHE1
    SUBNET --> CACHE2
    SUBNET --> CACHE3
```

## 使用方式

### 基本使用（單節點）

```hcl
module "elasticache" {
  source = "./modules/elasticache"

  identifier    = "my-cache"
  node_type     = "cache.t4g.medium"
  num_cache_nodes = 1
  
  # 網路配置
  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets
  
  # 安全配置
  allowed_cidr_blocks     = ["172.11.0.0/16"]
  allowed_security_groups = [module.eks.node_security_group_id]
  
  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

### 高可用性配置（多節點）

```hcl
module "elasticache" {
  source = "./modules/elasticache"

  identifier      = "production-cache"
  node_type       = "cache.r6g.large"
  num_cache_nodes = 3  # 1 主節點 + 2 副本節點
  
  # 引擎配置
  engine         = "valkey"
  engine_version = "7.2"
  port           = 6379
  
  # 網路配置
  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets
  
  # 參數群組（可選）
  parameter_group_name = "default.valkey7"
  
  # 安全配置
  allowed_cidr_blocks = ["10.0.0.0/8"]
  allowed_security_groups = [
    module.eks.node_security_group_id,
    module.app.security_group_id
  ]
  
  tags = {
    Environment        = "prod"
    Project           = "production-app"
    CostCenter        = "engineering"
    DataClassification = "internal"
  }
}
```

## 輸入變數

| 名稱 | 描述 | 類型 | 預設值 | 必要 |
|------|------|------|--------|------|
| `identifier` | ElastiCache 複製群組識別碼 | `string` | - | ✅ |
| `node_type` | 快取節點實例類型 | `string` | - | ✅ |
| `num_cache_nodes` | 快取節點數量 | `number` | - | ✅ |
| `vpc_id` | VPC ID | `string` | - | ✅ |
| `subnet_ids` | 子網路 ID 列表 | `list(string)` | - | ✅ |
| `engine` | 快取引擎 | `string` | `"valkey"` | ❌ |
| `engine_version` | 引擎版本 | `string` | `"7.2"` | ❌ |
| `port` | 快取連接埠 | `number` | `6379` | ❌ |
| `parameter_group_name` | 參數群組名稱 | `string` | `"default.valkey7"` | ❌ |
| `allowed_cidr_blocks` | 允許訪問的 CIDR 區塊 | `list(string)` | `[]` | ❌ |
| `allowed_security_groups` | 允許訪問的安全群組 | `list(string)` | `[]` | ❌ |
| `tags` | 資源標籤 | `map(string)` | `{}` | ❌ |

## 輸出值

| 名稱 | 描述 |
|------|------|
| `replication_group_id` | 複製群組識別碼 |
| `primary_endpoint_address` | 主要端點地址 |
| `reader_endpoint_address` | 讀取端點地址（如果適用） |
| `port` | 快取連接埠 |
| `security_group_id` | 快取安全群組 ID |
| `subnet_group_name` | 快取子網路群組名稱 |

## 實例類型建議

### 開發環境
```hcl
node_type = "cache.t4g.micro"   # 2 vCPU, 0.5 GB RAM
# 或
node_type = "cache.t4g.small"   # 2 vCPU, 1.5 GB RAM
```

### 測試環境
```hcl
node_type = "cache.t4g.medium"  # 2 vCPU, 3.2 GB RAM
# 或
node_type = "cache.r6g.large"   # 2 vCPU, 12.3 GB RAM
```

### 生產環境
```hcl
node_type = "cache.r6g.xlarge"   # 4 vCPU, 25.05 GB RAM
# 或
node_type = "cache.r6g.2xlarge"  # 8 vCPU, 50.47 GB RAM
```

## 節點配置策略

### 單節點配置（開發/測試）
```hcl
num_cache_nodes = 1
# 特點：
# - 成本最低
# - 無高可用性
# - 適合開發和測試環境
```

### 多節點配置（生產）
```hcl
num_cache_nodes = 3  # 1 主 + 2 副本
# 特點：
# - 自動故障轉移
# - 讀取負載分散
# - 高可用性
```

## 最佳實踐

### 1. 高可用性設計
```hcl
# 生產環境建議配置
module "elasticache" {
  source = "./modules/elasticache"
  
  identifier      = "prod-cache"
  node_type       = "cache.r6g.large"
  num_cache_nodes = 3  # 確保高可用性
  
  # 分散在多個可用區域
  subnet_ids = module.vpc.private_subnets
}
```

### 2. 安全性配置
- 部署在私有子網路
- 限制安全群組訪問
- 使用 VPC 內部通信
- 定期更新引擎版本

### 3. 效能優化
- 選擇適當的實例類型
- 使用記憶體優化實例（r6g 系列）
- 配置適當的參數群組
- 監控記憶體使用率

### 4. 成本優化
- 開發環境使用 t4g 系列
- 生產環境使用 r6g 系列
- 根據實際需求調整節點數量
- 定期檢查使用率

## 連接範例

### Python (redis-py)
```python
import redis

# 連接到 ElastiCache
r = redis.Redis(
    host='your-cache-cluster.cache.amazonaws.com',
    port=6379,
    decode_responses=True
)

# 基本操作
r.set('key', 'value')
value = r.get('key')
```

### Node.js (ioredis)
```javascript
const Redis = require('ioredis');

const redis = new Redis({
  host: 'your-cache-cluster.cache.amazonaws.com',
  port: 6379,
});

// 基本操作
await redis.set('key', 'value');
const value = await redis.get('key');
```

### Java (Jedis)
```java
import redis.clients.jedis.Jedis;

Jedis jedis = new Jedis(
    "your-cache-cluster.cache.amazonaws.com", 
    6379
);

// 基本操作
jedis.set("key", "value");
String value = jedis.get("key");
```

## 故障排除

### 常見問題

#### 1. 連接超時
**問題**：應用程式無法連接到快取
```
Error: timeout connecting to cache
```

**解決方案**：
- 檢查安全群組規則（確保 6379 埠開放）
- 確認子網路路由配置
- 驗證 DNS 解析

#### 2. 記憶體不足
**問題**：快取記憶體使用率過高

**解決方案**：
```bash
# 檢查記憶體使用情況
redis-cli info memory

# 清理過期鍵
redis-cli --scan --pattern "*" | xargs redis-cli del
```

#### 3. 效能問題
**問題**：快取回應緩慢

**解決方案**：
- 檢查網路延遲
- 監控 CPU 使用率
- 考慮升級實例類型
- 檢查慢查詢日誌

### 監控指標

```bash
# 檢查叢集狀態
aws elasticache describe-replication-groups \
  --replication-group-id <replication-group-id>

# 檢查節點狀態
aws elasticache describe-cache-clusters \
  --cache-cluster-id <cache-cluster-id>

# 檢查效能指標
aws cloudwatch get-metric-statistics \
  --namespace AWS/ElastiCache \
  --metric-name CPUUtilization \
  --dimensions Name=CacheClusterId,Value=<cache-cluster-id>
```

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0

## 相關資源

- [Amazon ElastiCache 用戶指南](https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/)
- [Valkey 文檔](https://valkey.io/docs/)
- [Redis 命令參考](https://redis.io/commands/)
- [ElastiCache 最佳實踐](https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/BestPractices.html)

## 範例

查看 [examples](../../examples/) 目錄以獲取更多使用範例。
