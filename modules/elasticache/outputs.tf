output "cluster_id" {
  description = "ElastiCache 叢集 ID"
  value       = aws_elasticache_replication_group.this.id
}

output "cluster_endpoint" {
  description = "ElastiCache 叢集端點"
  value       = aws_elasticache_replication_group.this.primary_endpoint_address
}

output "cluster_address" {
  description = "ElastiCache 叢集地址"
  value       = split(":", aws_elasticache_replication_group.this.primary_endpoint_address)[0]
}

output "cluster_port" {
  description = "ElastiCache 叢集連接埠"
  value       = var.port
}

output "security_group_id" {
  description = "ElastiCache 安全群組 ID"
  value       = aws_security_group.this.id
}

output "subnet_group_name" {
  description = "ElastiCache 子網路群組名稱"
  value       = aws_elasticache_subnet_group.this.name
}
