# GitHub Actions IAM 模組

## 概覽

此模組建立 GitHub Actions 所需的 IAM 資源，包括 OIDC provider 和 IAM role，提供 ECR 操作和 EKS 部署的權限。

## 功能特性

- ✅ **GitHub OIDC Provider**：建立或使用現有的 GitHub OIDC provider
- ✅ **IAM Role**：專為 GitHub Actions 設計的 IAM role
- ✅ **ECR 權限**：完整的 ECR 操作權限（push/pull images）
- ✅ **EKS 權限**：EKS 叢集存取和部署權限
- ✅ **安全限制**：限制特定 GitHub repository 和 branch
- ✅ **彈性配置**：支援額外的 policies 和 assume roles
- ✅ **驗證機制**：輸入參數驗證和格式檢查

## 使用方式

### 基本配置（無 repository 限制）

```hcl
module "github_actions_iam" {
  source = "./modules/github-actions-iam"

  github_org               = "your-org"
  enable_repo_restrictions = false  # 允許組織內所有 repositories
  cluster_name            = "your-eks-cluster"
  region                  = "ap-east-2"

  tags = {
    Environment = "production"
    Project     = "your-project"
  }
}
```

### 啟用 Repository 限制的配置

```hcl
module "github_actions_iam" {
  source = "./modules/github-actions-iam"

  github_org               = "your-org"
  github_repo              = "your-repo"
  enable_repo_restrictions = true
  github_branches          = ["main", "develop"]
  cluster_name            = "your-eks-cluster"
  region                  = "ap-east-2"

  tags = {
    Environment = "production"
    Project     = "your-project"
  }
}
```

### 完整配置（無限制）

```hcl
module "github_actions_iam" {
  source = "./modules/github-actions-iam"

  role_name                = "longshun-prod-github-actions"
  github_org               = "your-org"
  enable_repo_restrictions = false  # 允許組織內所有 repositories 和分支

  cluster_name = "longshun-prod-cluster"
  region       = "ap-east-2"

  # ECR repositories 存取權限
  ecr_repository_arns = [
    "arn:aws:ecr:ap-east-2:123456789012:repository/your-app",
    "arn:aws:ecr:ap-east-2:123456789012:repository/your-api"
  ]

  # 額外的 assume role 權限
  additional_assume_role_arns = [
    "arn:aws:iam::123456789012:role/eks-deployment-role"
  ]

  # 額外的 managed policies
  additional_policy_arns = [
    "arn:aws:iam::aws:policy/ReadOnlyAccess"
  ]

  tags = {
    Environment        = "production"
    Project           = "longshun"
    CostCenter        = "engineering"
    DataClassification = "internal"
  }
}
```

## GitHub Actions Workflow 範例

```yaml
name: Deploy to EKS

on:
  push:
    branches: [main]

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::123456789012:role/longshun-prod-github-actions
          role-session-name: GitHubActions
          aws-region: ap-east-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: your-app
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Update EKS deployment
        run: |
          aws eks update-kubeconfig --region ap-east-2 --name longshun-prod-cluster
          kubectl set image deployment/your-app your-app=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
```

## 輸入變數

| 變數名稱 | 類型 | 預設值 | 描述 |
|---------|------|--------|------|
| `role_name` | string | `"longshun-prod-github-actions"` | IAM role 名稱 |
| `github_org` | string | - | GitHub 組織名稱 |
| `github_repo` | string | `""` | GitHub repository 名稱（啟用限制時必填） |
| `github_branches` | list(string) | `["main", "master", "develop"]` | 允許的分支列表（啟用限制時使用） |
| `enable_repo_restrictions` | bool | `false` | 是否啟用 repository 和 branch 限制 |
| `cluster_name` | string | - | EKS 叢集名稱 |
| `region` | string | - | AWS 區域 |
| `ecr_repository_arns` | list(string) | `[]` | ECR repository ARNs |
| `additional_assume_role_arns` | list(string) | `[]` | 額外的 assume role ARNs |
| `additional_policy_arns` | list(string) | `[]` | 額外的 managed policy ARNs |
| `create_oidc_provider` | bool | `true` | 是否建立 OIDC provider |
| `existing_oidc_provider_arn` | string | `""` | 現有 OIDC provider ARN |

## 輸出值

| 輸出名稱 | 描述 |
|---------|------|
| `github_actions_role_arn` | GitHub Actions IAM role ARN |
| `github_actions_role_name` | GitHub Actions IAM role 名稱 |
| `github_oidc_provider_arn` | GitHub OIDC provider ARN |
| `ecr_policy_arn` | ECR 操作 policy ARN |
| `eks_deploy_policy_arn` | EKS 部署 policy ARN |

## 安全考量

1. **最小權限原則**：只授予必要的權限
2. **Repository 限制**：限制特定的 GitHub repository 和 branch
3. **ECR 存取控制**：可指定特定的 ECR repositories
4. **Session 管理**：使用短期的 session tokens
5. **審計追蹤**：所有操作都會記錄在 CloudTrail 中

## 故障排除

### 常見問題

1. **OIDC Provider 已存在**
   - 設定 `create_oidc_provider = false`
   - 提供 `existing_oidc_provider_arn`

2. **權限不足**
   - 檢查 ECR repository ARNs 是否正確
   - 確認 GitHub repository 和 branch 設定

3. **Trust relationship 錯誤**
   - 驗證 GitHub 組織和 repository 名稱
   - 檢查分支名稱是否正確

## 版本要求

- **Terraform**: >= 1.5.0
- **AWS Provider**: ~> 5.0
