output "github_actions_role_arn" {
  description = "GitHub Actions IAM role ARN"
  value       = aws_iam_role.github_actions.arn
}

output "github_actions_role_name" {
  description = "GitHub Actions IAM role 名稱"
  value       = aws_iam_role.github_actions.name
}

output "github_oidc_provider_arn" {
  description = "GitHub OIDC provider ARN"
  value       = var.create_oidc_provider ? aws_iam_openid_connect_provider.github[0].arn : var.existing_oidc_provider_arn
}

output "ecr_policy_arn" {
  description = "ECR 操作 policy ARN"
  value       = aws_iam_policy.ecr_policy.arn
}

output "eks_deploy_policy_arn" {
  description = "EKS 部署 policy ARN"
  value       = aws_iam_policy.eks_deploy_policy.arn
}

output "github_actions_assume_role_command" {
  description = "GitHub Actions 中使用的 AWS configure 命令範例"
  value       = "aws-actions/configure-aws-credentials@v4 with role-to-assume: ${aws_iam_role.github_actions.arn}"
}

output "github_workflow_example" {
  description = "GitHub Actions workflow 配置範例"
  value = {
    permissions = {
      id-token = "write"
      contents = "read"
    }
    steps = [
      {
        name = "Configure AWS credentials"
        uses = "aws-actions/configure-aws-credentials@v4"
        with = {
          role-to-assume    = aws_iam_role.github_actions.arn
          role-session-name = "GitHubActions"
          aws-region        = var.region
        }
      }
    ]
  }
}

# 輸出允許的 GitHub repositories 和 branches 資訊
output "allowed_github_repos" {
  description = "允許使用此 role 的 GitHub repositories 和 branches"
  value = {
    organization         = var.github_org
    repository           = var.enable_repo_restrictions ? var.github_repo : "all repositories in organization"
    branches             = var.enable_repo_restrictions ? var.github_branches : ["all branches"]
    restrictions_enabled = var.enable_repo_restrictions
  }
}

# 輸出 ECR 存取權限資訊
output "ecr_access_info" {
  description = "ECR 存取權限資訊"
  value = {
    repository_arns = local.effective_ecr_arns
    wildcard_access = var.enable_wildcard_ecr_access
  }
}

# 輸出 EKS 存取權限資訊
output "eks_access_info" {
  description = "EKS 存取權限資訊"
  value = {
    cluster_name                = var.cluster_name
    region                      = var.region
    additional_assume_role_arns = var.additional_assume_role_arns
  }
}
