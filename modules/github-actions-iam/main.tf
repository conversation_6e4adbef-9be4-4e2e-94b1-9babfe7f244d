# GitHub Actions IAM 模組
# 建立 GitHub OIDC provider 和 IAM role 供 GitHub Actions 使用

# 取得目前的 AWS 帳戶 ID
data "aws_caller_identity" "current" {}

# 建立 GitHub OIDC Provider（如果不存在）
resource "aws_iam_openid_connect_provider" "github" {
  count = var.create_oidc_provider ? 1 : 0

  url = "https://token.actions.githubusercontent.com"

  client_id_list = [
    "sts.amazonaws.com",
  ]

  thumbprint_list = [
    "6938fd4d98bab03faadb97b34396831e3780aea1",
    "1c58a3a8518e8759bf075b76b750d4f2df264fcd"
  ]

  tags = var.tags
}

# 建立 IAM role 供 GitHub Actions 使用
resource "aws_iam_role" "github_actions" {
  name        = var.role_name
  description = "IAM role for GitHub Actions with ECR and EKS permissions"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Federated = var.create_oidc_provider ? aws_iam_openid_connect_provider.github[0].arn : var.existing_oidc_provider_arn
        }
        Action = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          StringEquals = {
            "token.actions.githubusercontent.com:aud" = "sts.amazonaws.com"
          }
          StringLike = {
            "token.actions.githubusercontent.com:sub" = var.enable_repo_restrictions ? [
              for branch in var.github_branches :
              "repo:${var.github_org}/${var.github_repo}:ref:refs/heads/${branch}"
            ] : ["repo:${var.github_org}/*"]
          }
        }
      }
    ]
  })

  tags = var.tags
}

# ECR 操作權限 policy
resource "aws_iam_policy" "ecr_policy" {
  name        = "${var.role_name}-ecr-policy"
  description = "ECR permissions for GitHub Actions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecr:GetAuthorizationToken"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:PutImage",
          "ecr:ListImages",
          "ecr:DescribeImages",
          "ecr:DescribeRepositories"
        ]
        Resource = length(local.effective_ecr_arns) > 0 ? local.effective_ecr_arns : ["*"]
      }
    ]
  })

  tags = var.tags
}

# EKS 部署權限 policy
resource "aws_iam_policy" "eks_deploy_policy" {
  name        = "${var.role_name}-eks-deploy-policy"
  description = "EKS deployment permissions for GitHub Actions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = concat([
      {
        Effect = "Allow"
        Action = [
          "eks:DescribeCluster",
          "eks:ListClusters",
          "eks:DescribeNodegroup",
          "eks:ListNodegroups",
          "eks:DescribeAddon",
          "eks:ListAddons"
        ]
        Resource = [
          "arn:aws:eks:${var.region}:${data.aws_caller_identity.current.account_id}:cluster/${var.cluster_name}",
          "arn:aws:eks:${var.region}:${data.aws_caller_identity.current.account_id}:cluster/${var.cluster_name}/*"
        ]
      }
      ], length(var.additional_assume_role_arns) > 0 ? [
      {
        Effect = "Allow"
        Action = [
          "sts:AssumeRole"
        ]
        Resource = var.additional_assume_role_arns
      }
    ] : [])
  })

  tags = var.tags
}

# 附加 ECR policy 到 role
resource "aws_iam_role_policy_attachment" "ecr_policy_attachment" {
  role       = aws_iam_role.github_actions.name
  policy_arn = aws_iam_policy.ecr_policy.arn
}

# 附加 EKS deploy policy 到 role
resource "aws_iam_role_policy_attachment" "eks_deploy_policy_attachment" {
  role       = aws_iam_role.github_actions.name
  policy_arn = aws_iam_policy.eks_deploy_policy.arn
}

# 附加額外的 managed policies（如果有）
resource "aws_iam_role_policy_attachment" "additional_policies" {
  for_each = toset(var.additional_policy_arns)

  role       = aws_iam_role.github_actions.name
  policy_arn = each.value
}
