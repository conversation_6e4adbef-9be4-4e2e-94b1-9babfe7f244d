variable "role_name" {
  description = "GitHub Actions IAM role 名稱"
  type        = string
  default     = "longshun-prod-github-actions"
}

variable "github_org" {
  description = "GitHub 組織名稱"
  type        = string
}

variable "github_repo" {
  description = "GitHub repository 名稱（當 enable_repo_restrictions 為 true 時必填）"
  type        = string
  default     = ""
}

variable "github_branches" {
  description = "允許使用此 role 的 GitHub 分支列表（當 enable_repo_restrictions 為 true 時使用）"
  type        = list(string)
  default     = ["main", "master", "develop"]
}

variable "enable_repo_restrictions" {
  description = "是否啟用 repository 和 branch 限制"
  type        = bool
  default     = false
}

variable "cluster_name" {
  description = "EKS 叢集名稱"
  type        = string
}

variable "region" {
  description = "AWS 區域"
  type        = string
}

variable "ecr_repository_arns" {
  description = "ECR repository ARNs 列表，GitHub Actions 可以存取這些 repositories"
  type        = list(string)
  default     = []
}

variable "additional_assume_role_arns" {
  description = "GitHub Actions 可以承擔的額外 IAM role ARNs"
  type        = list(string)
  default     = []
}

variable "additional_policy_arns" {
  description = "附加到 GitHub Actions role 的額外 managed policy ARNs"
  type        = list(string)
  default     = []
}

variable "create_oidc_provider" {
  description = "是否建立新的 GitHub OIDC provider"
  type        = bool
  default     = true
}

variable "existing_oidc_provider_arn" {
  description = "現有的 GitHub OIDC provider ARN（當 create_oidc_provider 為 false 時使用）"
  type        = string
  default     = ""
}

variable "tags" {
  description = "資源標籤"
  type        = map(string)
  default     = {}
}

# 驗證規則
variable "enable_wildcard_ecr_access" {
  description = "是否允許存取所有 ECR repositories（不建議在生產環境使用）"
  type        = bool
  default     = false
}

# 驗證
locals {
  # 如果沒有指定 ECR repository ARNs 且沒有啟用 wildcard 存取，則使用預設的 wildcard
  effective_ecr_arns = length(var.ecr_repository_arns) > 0 ? var.ecr_repository_arns : (
    var.enable_wildcard_ecr_access ? ["*"] : [
      "arn:aws:ecr:${var.region}:*:repository/*"
    ]
  )
}

# 驗證 GitHub 組織和 repository 名稱格式
variable "validate_github_names" {
  description = "是否驗證 GitHub 組織和 repository 名稱格式"
  type        = bool
  default     = true
}

# 驗證規則
locals {
  # GitHub 組織名稱驗證（只允許字母、數字、連字號）
  github_org_valid = can(regex("^[a-zA-Z0-9-]+$", var.github_org))

  # GitHub repository 名稱驗證（只在啟用限制時檢查）
  github_repo_valid = var.enable_repo_restrictions ? (
    var.github_repo != "" && can(regex("^[a-zA-Z0-9._-]+$", var.github_repo))
  ) : true

  # 分支名稱驗證（只在啟用限制時檢查）
  github_branches_valid = var.enable_repo_restrictions ? alltrue([
    for branch in var.github_branches : can(regex("^[a-zA-Z0-9._/-]+$", branch))
  ]) : true
}

# 驗證檢查
resource "null_resource" "validate_inputs" {
  count = var.validate_github_names ? 1 : 0

  lifecycle {
    precondition {
      condition     = local.github_org_valid
      error_message = "GitHub 組織名稱格式無效。只允許字母、數字和連字號。"
    }

    precondition {
      condition     = local.github_repo_valid
      error_message = "當啟用 repository 限制時，GitHub repository 名稱不能為空且格式必須有效。只允許字母、數字、點、底線和連字號。"
    }

    precondition {
      condition     = local.github_branches_valid
      error_message = "當啟用 repository 限制時，GitHub 分支名稱格式無效。只允許字母、數字、點、底線、連字號和斜線。"
    }

    precondition {
      condition     = var.create_oidc_provider || var.existing_oidc_provider_arn != ""
      error_message = "當 create_oidc_provider 為 false 時，必須提供 existing_oidc_provider_arn。"
    }

    precondition {
      condition     = !var.enable_repo_restrictions || var.github_repo != ""
      error_message = "當啟用 repository 限制時，必須提供 github_repo 參數。"
    }
  }
}
