# External DNS 的 IAM 角色已經在 external_dns 模組中創建

module "eks" {
  source = "./modules/eks"

  cluster_name       = var.cluster_name
  cluster_version    = var.cluster_version
  vpc_id             = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnets

  # 傳遞 Vault KMS 解封政策 ARN
  vault_kms_unseal_policy_arn = module.kms.kms_unseal_policy_arn

  # 傳遞允許的 CIDR 區塊
  allowed_cidr_blocks = var.allowed_cidr_blocks

  # 傳遞額外的 IAM 政策（如果有自訂需求）
  additional_iam_policies = var.additional_iam_policies

  # 傳遞 GitHub Actions 角色 ARN（如果啟用）
  github_actions_role_arn = var.enable_github_actions_iam ? module.github_actions_iam[0].github_actions_role_arn : ""

  node_groups = var.node_groups
  tags        = local.resource_specific_tags.eks_tags
}
