# ============================================================================
# Terraform Infrastructure Environment Configuration Example
# 複製此檔案為 .envrc 並修改相應的值
# ============================================================================

# ----------------------------------------------------------------------------
# AWS 認證設定
# ----------------------------------------------------------------------------
# 方法 1: 使用 AWS Profile (推薦)
# export AWS_PROFILE="your-aws-profile-name"

# 方法 2: 使用 AWS 存取金鑰 (不推薦用於生產環境)
# export AWS_ACCESS_KEY_ID="your-access-key-id"
# export AWS_SECRET_ACCESS_KEY="your-secret-access-key"

# 方法 3: 使用 AWS SSO (推薦用於企業環境)
# export AWS_PROFILE="your-sso-profile"

# AWS 區域設定
export AWS_REGION="ap-east-2"
export AWS_DEFAULT_REGION="ap-east-2"

# ----------------------------------------------------------------------------
# Terraform 設定
# ----------------------------------------------------------------------------
# Terraform 後端配置 (修改為您的實際值)
export TF_VAR_terraform_state_bucket="your-terraform-state-bucket"
export TF_VAR_terraform_state_key="infra/terraform.tfstate"
export TF_VAR_terraform_state_lock_table="your-terraform-state-lock-table"

# Terraform 工作區設定
export TF_WORKSPACE="dev"

# Terraform 日誌等級
export TF_LOG="INFO"

# Terraform 插件快取目錄
export TF_PLUGIN_CACHE_DIR="$HOME/.terraform.d/plugin-cache"

# ----------------------------------------------------------------------------
# 專案特定變數 (修改為您的實際值)
# ----------------------------------------------------------------------------
# 基本配置
export TF_VAR_region="ap-east-2"
export TF_VAR_environment="dev"
export TF_VAR_project_name="your-project-name"
export TF_VAR_application_name="infrastructure"
export TF_VAR_owner="your-team-name"

# EKS 叢集設定
export TF_VAR_cluster_name="your-cluster-name"
export TF_VAR_cluster_version="1.33"

# 網路設定
export TF_VAR_vpc_cidr="10.0.0.0/16"
export TF_VAR_availability_zones='["ap-east-2a", "ap-east-2b", "ap-east-2c"]'
export TF_VAR_private_subnets='["10.0.1.0/24", "10.0.2.0/24", "10.0.3.0/24"]'
export TF_VAR_public_subnets='["10.0.101.0/24", "10.0.102.0/24", "10.0.103.0/24"]'

# Route53 和 SSL 憑證設定
export TF_VAR_route53_zone_id="your-route53-zone-id"
export TF_VAR_domain_name="your-domain.com"
export TF_VAR_subject_alternative_names='["*.your-domain.com"]'

# RDS 設定
export TF_VAR_db_identifier="your-db-identifier"
export TF_VAR_db_instance_class="db.t4g.medium"
export TF_VAR_db_allocated_storage="20"
export TF_VAR_db_name="your-db-name"
export TF_VAR_db_username="postgres"

# ElastiCache 設定
export TF_VAR_elasticache_identifier="your-cache-identifier"
export TF_VAR_elasticache_node_type="cache.t4g.medium"
export TF_VAR_elasticache_num_nodes="1"
export TF_VAR_elasticache_engine_version="7.2"

# GitHub Actions IAM 設定
export TF_VAR_enable_github_actions_iam="true"
export TF_VAR_github_org="your-github-organization"
export TF_VAR_github_actions_create_oidc_provider="true"

# ----------------------------------------------------------------------------
# 開發工具設定
# ----------------------------------------------------------------------------
# Go 設定
export GO_TEST_TIMEOUT="30m"
export GOPATH="$HOME/go"

# Kubernetes 設定
export KUBECONFIG="$HOME/.kube/config"

# ----------------------------------------------------------------------------
# 安全性提醒
# ----------------------------------------------------------------------------
echo "⚠️  請確保："
echo "   1. 不要提交包含敏感資訊的 .envrc 到版本控制"
echo "   2. 使用 AWS CLI profiles 或 IAM roles 而非硬編碼金鑰"
echo "   3. 定期輪換 AWS 存取金鑰"
echo "   4. 為不同環境使用不同的配置"

# 檢查必要工具
if ! command -v terraform &> /dev/null; then
    echo "❌ Terraform 未安裝"
fi

if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI 未安裝"
fi

# 建立必要目錄
mkdir -p "$TF_PLUGIN_CACHE_DIR"

# 專案路徑設定
if [ -d "./scripts" ]; then
    export PATH="./scripts:$PATH"
fi

if [ -d "./tests/scripts" ]; then
    export PATH="./tests/scripts:$PATH"
fi

# 開發環境提示
echo "🚀 開發環境已載入"
echo "📁 專案: $(basename "$PWD")"
echo "🌍 AWS 區域: $AWS_REGION"
echo "🏗️  環境: ${TF_VAR_environment:-dev}"
