provider "aws" {
  region                 = var.region
  skip_region_validation = true
}

# kubectl provider 配置
provider "kubectl" {
  host                   = module.eks.cluster_endpoint
  cluster_ca_certificate = base64decode(module.eks.certificate_authority_data)
  token                  = data.aws_eks_cluster_auth.cluster.token
  load_config_file       = false
}

# 獲取 EKS 叢集認證
data "aws_eks_cluster_auth" "cluster" {
  name = "longshun-tpe"
}

# 使用 random_password 資源生成密碼
resource "random_password" "db_password" {
  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
  min_special      = 2
  min_upper        = 2
  min_lower        = 2
  min_numeric      = 2
}
