variable "region" {
  description = "AWS 區域"
  type        = string
  default     = "ap-east-2"

  validation {
    condition     = can(regex("^(us|eu|ap|sa|ca|af|me|il)-(north|south|east|west|central|northeast|southeast|southwest|northwest)-[1-9]$", var.region))
    error_message = "區域必須是有效的 AWS 區域格式，例如：us-east-1, ap-east-2, eu-west-1。"
  }
}

variable "vpc_cidr" {
  description = "VPC CIDR 區塊"
  type        = string

  validation {
    condition     = can(cidrhost(var.vpc_cidr, 0))
    error_message = "VPC CIDR 必須是有效的 CIDR 格式，例如：10.0.0.0/16 或 **********/12。"
  }

  validation {
    condition     = tonumber(split("/", var.vpc_cidr)[1]) >= 8 && tonumber(split("/", var.vpc_cidr)[1]) <= 28
    error_message = "VPC CIDR 的子網路遮罩必須在 /8 到 /28 之間。"
  }
}

variable "availability_zones" {
  description = "可用區域列表"
  type        = list(string)
  default     = ["ap-east-2a", "ap-east-2b", "ap-east-2c"]

  validation {
    condition     = length(var.availability_zones) >= 2 && length(var.availability_zones) <= 6
    error_message = "可用區域列表必須包含 2 到 6 個可用區域。"
  }

  validation {
    condition = alltrue([
      for az in var.availability_zones : can(regex("^[a-z]{2}-[a-z]+-[0-9][a-z]$", az))
    ])
    error_message = "所有可用區域必須是有效的 AWS 可用區域格式，例如：us-east-1a, ap-east-2b。"
  }
}

variable "private_subnets" {
  description = "私有子網 CIDR 區塊列表"
  type        = list(string)

  validation {
    condition = alltrue([
      for cidr in var.private_subnets : can(cidrhost(cidr, 0))
    ])
    error_message = "所有私有子網 CIDR 必須是有效的 CIDR 格式。"
  }

  validation {
    condition     = length(var.private_subnets) >= 2 && length(var.private_subnets) <= 6
    error_message = "私有子網列表必須包含 2 到 6 個子網。"
  }
}

variable "public_subnets" {
  description = "公有子網 CIDR 區塊列表"
  type        = list(string)

  validation {
    condition = alltrue([
      for cidr in var.public_subnets : can(cidrhost(cidr, 0))
    ])
    error_message = "所有公有子網 CIDR 必須是有效的 CIDR 格式。"
  }

  validation {
    condition     = length(var.public_subnets) >= 2 && length(var.public_subnets) <= 6
    error_message = "公有子網列表必須包含 2 到 6 個子網。"
  }
}

variable "cluster_name" {
  description = "EKS 叢集名稱"
  type        = string

  validation {
    condition     = can(regex("^[a-zA-Z][a-zA-Z0-9-]*[a-zA-Z0-9]$", var.cluster_name))
    error_message = "叢集名稱必須以字母開頭，以字母或數字結尾，只能包含字母、數字和連字號。"
  }

  validation {
    condition     = length(var.cluster_name) >= 1 && length(var.cluster_name) <= 100
    error_message = "叢集名稱長度必須在 1 到 100 個字元之間。"
  }
}

variable "cluster_version" {
  description = "EKS 叢集 Kubernetes 版本"
  type        = string
  default     = "1.33"

  validation {
    condition     = can(regex("^1\\.(2[8-9]|3[0-9])$", var.cluster_version))
    error_message = "Kubernetes 版本必須是 1.28 或更高版本，格式為 1.xx。"
  }
}

variable "node_groups" {
  description = "EKS 節點群組配置"
  type = map(object({
    desired_capacity = number
    max_capacity     = number
    min_capacity     = number
    instance_types   = list(string)
  }))
  default = {
    default_node_group = {
      desired_capacity = 2
      max_capacity     = 3
      min_capacity     = 1
      instance_types   = ["t4g.medium"]
    }
  }

  validation {
    condition = alltrue([
      for name, config in var.node_groups : (
        config.min_capacity >= 0 &&
        config.max_capacity >= config.min_capacity &&
        config.desired_capacity >= config.min_capacity &&
        config.desired_capacity <= config.max_capacity &&
        config.max_capacity <= 1000
      )
    ])
    error_message = "節點群組容量設定必須滿足：0 <= min_capacity <= desired_capacity <= max_capacity <= 1000。"
  }

  validation {
    condition = alltrue([
      for name, config in var.node_groups : alltrue([
        for instance_type in config.instance_types : can(regex("^[a-z][0-9][a-z]*\\.[a-z0-9]+$", instance_type))
      ])
    ])
    error_message = "實例類型必須是有效的 EC2 實例類型格式，例如：t3.medium, m5.large。"
  }
}

# ============================================================================
# 增強標籤策略變數
# ============================================================================

# 核心標籤變數
variable "environment" {
  description = "部署環境"
  type        = string
  default     = "dev"

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "環境必須是 dev、staging 或 prod 之一。"
  }
}

variable "project_name" {
  description = "專案名稱"
  type        = string
  default     = "taipei-infra"

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project_name))
    error_message = "專案名稱只能包含小寫字母、數字和連字符。"
  }
}

variable "application_name" {
  description = "應用程式名稱"
  type        = string
  default     = "infrastructure"

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.application_name))
    error_message = "應用程式名稱只能包含小寫字母、數字和連字符。"
  }
}

variable "owner" {
  description = "資源負責人或團隊"
  type        = string
  default     = "infrastructure-team"
}

# 成本管理標籤變數
variable "cost_center" {
  description = "成本中心"
  type        = string
  default     = "engineering"
}

variable "business_unit" {
  description = "業務單位"
  type        = string
  default     = "technology"
}

variable "billing_code" {
  description = "計費代碼"
  type        = string
  default     = "infra-001"
}

variable "budget" {
  description = "預算類別"
  type        = string
  default     = "infrastructure"
}

# 運維標籤變數
variable "created_by" {
  description = "資源創建者"
  type        = string
  default     = "terraform"
}

variable "infrastructure_version" {
  description = "基礎設施版本"
  type        = string
  default     = "1.0.0"
}

variable "maintenance_window" {
  description = "維護窗口 (格式: day:HH:MM-HH:MM)"
  type        = string
  default     = "sun:03:00-05:00"

  validation {
    condition     = can(regex("^(mon|tue|wed|thu|fri|sat|sun):[0-2][0-9]:[0-5][0-9]-[0-2][0-9]:[0-5][0-9]$", var.maintenance_window))
    error_message = "維護窗口格式必須是 day:HH:MM-HH:MM，例如 sun:03:00-05:00。"
  }
}

variable "backup_required" {
  description = "是否需要備份"
  type        = string
  default     = "true"

  validation {
    condition     = contains(["true", "false"], var.backup_required)
    error_message = "備份要求必須是 true 或 false。"
  }
}

variable "monitoring_level" {
  description = "監控級別"
  type        = string
  default     = "standard"

  validation {
    condition     = contains(["basic", "standard", "enhanced", "critical"], var.monitoring_level)
    error_message = "監控級別必須是 basic、standard、enhanced 或 critical 之一。"
  }
}

# 安全和合規標籤變數
variable "data_classification" {
  description = "資料分類級別"
  type        = string
  default     = "internal"

  validation {
    condition     = contains(["public", "internal", "confidential", "restricted"], var.data_classification)
    error_message = "資料分類必須是 public、internal、confidential 或 restricted 之一。"
  }
}

variable "security_level" {
  description = "安全級別"
  type        = string
  default     = "medium"

  validation {
    condition     = contains(["low", "medium", "high", "critical"], var.security_level)
    error_message = "安全級別必須是 low、medium、high 或 critical 之一。"
  }
}

variable "compliance_requirements" {
  description = "合規要求"
  type        = string
  default     = "none"
}

# 自動化標籤變數
variable "auto_start" {
  description = "是否自動啟動"
  type        = string
  default     = "false"

  validation {
    condition     = contains(["true", "false"], var.auto_start)
    error_message = "自動啟動必須是 true 或 false。"
  }
}

variable "auto_stop" {
  description = "是否自動停止"
  type        = string
  default     = "false"

  validation {
    condition     = contains(["true", "false"], var.auto_stop)
    error_message = "自動停止必須是 true 或 false。"
  }
}

variable "lifecycle_policy" {
  description = "生命週期策略"
  type        = string
  default     = "standard"

  validation {
    condition     = contains(["standard", "extended", "minimal"], var.lifecycle_policy)
    error_message = "生命週期策略必須是 standard、extended 或 minimal 之一。"
  }
}

# 原有標籤變數 (保持向後相容性)
variable "tags" {
  description = "額外的資源標籤 (會與標準標籤合併)"
  type        = map(string)
  default     = {}

  validation {
    condition = alltrue([
      for key, value in var.tags : can(regex("^[a-zA-Z0-9+\\-=._:/@]+$", key))
    ])
    error_message = "標籤鍵只能包含字母、數字和特殊字符 +\\-=._:/@。"
  }

  validation {
    condition = alltrue([
      for key, value in var.tags : length(key) <= 128
    ])
    error_message = "標籤鍵長度不能超過 128 個字符。"
  }

  validation {
    condition = alltrue([
      for key, value in var.tags : length(value) <= 256
    ])
    error_message = "標籤值長度不能超過 256 個字符。"
  }
}

# RDS 相關變數
variable "db_identifier" {
  description = "RDS 執行個體識別碼"
  type        = string
  default     = "taipei-postgres"
}

variable "db_instance_class" {
  description = "RDS 執行個體類型"
  type        = string
  default     = "db.t4g.medium"

  validation {
    condition     = can(regex("^db\\.[a-z][0-9][a-z]*\\.[a-z0-9]+$", var.db_instance_class))
    error_message = "RDS 實例類型必須是有效的格式，例如：db.t3.micro, db.r5.large。"
  }
}

variable "db_allocated_storage" {
  description = "分配的儲存空間大小 (GB)"
  type        = number
  default     = 20

  validation {
    condition     = var.db_allocated_storage >= 20 && var.db_allocated_storage <= 65536
    error_message = "RDS 儲存空間必須在 20GB 到 65536GB 之間。"
  }
}

variable "db_name" {
  description = "資料庫名稱"
  type        = string
  default     = "wb"

  validation {
    condition     = can(regex("^[a-zA-Z][a-zA-Z0-9_]*$", var.db_name))
    error_message = "資料庫名稱必須以字母開頭，只能包含字母、數字和底線。"
  }

  validation {
    condition     = length(var.db_name) >= 1 && length(var.db_name) <= 63
    error_message = "資料庫名稱長度必須在 1 到 63 個字元之間。"
  }
}

variable "db_username" {
  description = "資料庫管理員使用者名稱"
  type        = string
  default     = "postgres"

  validation {
    condition     = can(regex("^[a-zA-Z][a-zA-Z0-9_]*$", var.db_username))
    error_message = "資料庫使用者名稱必須以字母開頭，只能包含字母、數字和底線。"
  }

  validation {
    condition     = length(var.db_username) >= 1 && length(var.db_username) <= 63
    error_message = "資料庫使用者名稱長度必須在 1 到 63 個字元之間。"
  }
}

# Backend 和基礎設施相關變數
variable "terraform_state_bucket" {
  description = "Terraform 狀態檔案的 S3 儲存桶名稱"
  type        = string
  default     = "longshun-taipei-terraform"
}

variable "terraform_state_key" {
  description = "Terraform 狀態檔案在 S3 中的路徑"
  type        = string
  default     = "infra/terraform.tfstate"
}

variable "terraform_state_lock_table" {
  description = "Terraform state locking 的 DynamoDB 表名稱"
  type        = string
  default     = null

  validation {
    condition     = var.terraform_state_lock_table == null || can(regex("^[a-zA-Z0-9._-]+$", var.terraform_state_lock_table))
    error_message = "DynamoDB 表名稱只能包含字母、數字、點號、底線和連字號。"
  }
}

# Route53 相關變數
variable "route53_zone_id" {
  description = "現有的 Route53 託管區域 ID"
  type        = string
  default     = "Z04339392B2NE9OVTLRBO"

  validation {
    condition     = can(regex("^Z[A-Z0-9]{10,32}$", var.route53_zone_id))
    error_message = "Route53 託管區域 ID 必須以 Z 開頭，後跟 10-32 個大寫字母或數字。"
  }
}

variable "domain_name" {
  description = "主要網域名稱"
  type        = string
  default     = "longshun.io"

  validation {
    condition     = can(regex("^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\\.[a-zA-Z]{2,}$", var.domain_name))
    error_message = "網域名稱必須是有效的格式，例如：example.com, my-domain.org。"
  }
}

variable "subject_alternative_names" {
  description = "SSL 憑證的主體備用名稱"
  type        = list(string)
  default     = ["*.longshun.io"]
}

# 網路 CIDR 相關變數
variable "allowed_cidr_blocks" {
  description = "允許訪問資源的 CIDR 區塊列表"
  type        = list(string)
  default     = ["**********/16"]

  validation {
    condition = alltrue([
      for cidr in var.allowed_cidr_blocks : can(cidrhost(cidr, 0))
    ])
    error_message = "所有 CIDR 區塊必須是有效的 CIDR 格式。"
  }

  validation {
    condition     = length(var.allowed_cidr_blocks) >= 1 && length(var.allowed_cidr_blocks) <= 10
    error_message = "允許的 CIDR 區塊列表必須包含 1 到 10 個項目。"
  }
}

# IAM 政策相關變數
variable "additional_iam_policies" {
  description = "附加到 EKS 節點群組的額外 IAM 政策 ARN 列表"
  type        = list(string)
  default = [
    "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy",
    "arn:aws:iam::aws:policy/AmazonKMSFullAccess"
  ]

  validation {
    condition = alltrue([
      for arn in var.additional_iam_policies : can(regex("^arn:aws:iam::(aws|[0-9]{12}):policy/.*$", arn))
    ])
    error_message = "所有 IAM 政策 ARN 必須是有效的格式，例如：arn:aws:iam::aws:policy/PolicyName 或 arn:aws:iam::123456789012:policy/PolicyName。"
  }
}

# EKS 附加元件相關變數
variable "enable_aws_load_balancer_controller" {
  description = "是否啟用 AWS Load Balancer Controller"
  type        = bool
  default     = true
}

# GitHub Actions IAM 相關變數
variable "github_org" {
  description = "GitHub 組織名稱"
  type        = string
  default     = ""
}

variable "github_repo" {
  description = "GitHub repository 名稱"
  type        = string
  default     = ""
}

variable "github_allowed_branches" {
  description = "允許使用 GitHub Actions IAM role 的分支列表（當啟用 repository 限制時使用）"
  type        = list(string)
  default     = ["main", "master", "develop"]
}

variable "github_enable_repo_restrictions" {
  description = "是否啟用 GitHub repository 和 branch 限制"
  type        = bool
  default     = false
}

variable "github_actions_ecr_repository_arns" {
  description = "GitHub Actions 可以存取的 ECR repository ARNs 列表"
  type        = list(string)
  default     = []
}

variable "github_actions_additional_assume_role_arns" {
  description = "GitHub Actions 可以承擔的額外 IAM role ARNs"
  type        = list(string)
  default     = []
}

variable "github_actions_additional_policy_arns" {
  description = "附加到 GitHub Actions IAM role 的額外 managed policy ARNs"
  type        = list(string)
  default     = []
}

variable "github_actions_create_oidc_provider" {
  description = "是否建立新的 GitHub OIDC provider"
  type        = bool
  default     = true
}

variable "github_actions_existing_oidc_provider_arn" {
  description = "現有的 GitHub OIDC provider ARN（當 github_actions_create_oidc_provider 為 false 時使用）"
  type        = string
  default     = ""
}

variable "enable_github_actions_iam" {
  description = "是否啟用 GitHub Actions IAM role"
  type        = bool
  default     = false
}

variable "aws_load_balancer_controller_chart_version" {
  description = "AWS Load Balancer Controller Helm Chart 版本"
  type        = string
  default     = "1.6.2"
}

variable "enable_cluster_autoscaler" {
  description = "是否啟用 Cluster Autoscaler"
  type        = bool
  default     = true
}

variable "cluster_autoscaler_chart_version" {
  description = "Cluster Autoscaler Helm Chart 版本"
  type        = string
  default     = "9.29.0"
}

variable "enable_external_dns" {
  description = "是否啟用 External DNS"
  type        = bool
  default     = true
}

variable "external_dns_chart_version" {
  description = "External DNS Helm Chart 版本"
  type        = string
  default     = "1.13.1" # 最新穩定版本
}

variable "external_dns_create_namespace" {
  description = "是否建立 External DNS 命名空間"
  type        = bool
  default     = true
}

variable "external_dns_namespace" {
  description = "External DNS 命名空間"
  type        = string
  default     = "external-dns"
}

variable "external_dns_service_account_name" {
  description = "External DNS 服務帳號名稱"
  type        = string
  default     = "external-dns"
}

variable "external_dns_domain_filters" {
  description = "限制哪些域名會被同步 (e.g. ['example.com'])"
  type        = list(string)
  default     = []
}

variable "external_dns_txt_prefix" {
  description = "TXT 記錄前綴，用於避免 DNS 名稱衝突問題"
  type        = string
  default     = "cname-" # 預設使用 cname- 作為前綴
}

variable "external_dns_policy" {
  description = "告訴 External DNS 應如何同步 DNS 記錄 (sync, upsert-only)"
  type        = string
  default     = "upsert-only"

  validation {
    condition     = contains(["sync", "upsert-only"], var.external_dns_policy)
    error_message = "External DNS 政策必須是 'sync' 或 'upsert-only'。"
  }
}

variable "external_dns_hosted_zone_arns" {
  description = "External DNS 可以管理的 Route53 託管區域 ARN 列表"
  type        = list(string)
  default     = ["arn:aws:route53:::hostedzone/*"]
}

variable "external_dns_zone_type" {
  description = "Route53 區域類型 (public, private)"
  type        = string
  default     = "public"

  validation {
    condition     = contains(["public", "private"], var.external_dns_zone_type)
    error_message = "Route53 區域類型必須是 'public' 或 'private'。"
  }
}

variable "external_dns_txt_owner_id" {
  description = "TXT 記錄擁有者識別碼，用於識別由 External DNS 創建的記錄"
  type        = string
  default     = ""
}

# External Secrets 變數
variable "enable_external_secrets" {
  description = "是否啟用 External Secrets"
  type        = bool
  default     = true
}

variable "external_secrets_chart_version" {
  description = "External Secrets Helm Chart 版本"
  type        = string
  default     = "0.9.9"
}

variable "external_secrets_create_namespace" {
  description = "是否建立 External Secrets 命名空間"
  type        = bool
  default     = true
}

variable "external_secrets_namespace" {
  description = "External Secrets 命名空間"
  type        = string
  default     = "external-secrets"
}

# Metrics Server 變數
variable "enable_metrics_server" {
  description = "是否啟用 Metrics Server"
  type        = bool
  default     = true
}

variable "metrics_server_addon_version" {
  description = "Metrics Server EKS Addon 版本"
  type        = string
  default     = "v0.7.2-eksbuild.2" # AWS EKS Addon 對應版本
}

variable "metrics_server_create_namespace" {
  description = "是否建立 Metrics Server 命名空間"
  type        = bool
  default     = false
}

variable "metrics_server_namespace" {
  description = "Metrics Server 命名空間"
  type        = string
  default     = "kube-system"
}

# Reloader 變數
variable "enable_reloader" {
  description = "是否啟用 Reloader"
  type        = bool
  default     = true
}

variable "reloader_chart_version" {
  description = "Reloader Helm Chart 版本"
  type        = string
  default     = "1.0.55"
}

variable "reloader_create_namespace" {
  description = "是否建立 Reloader 命名空間"
  type        = bool
  default     = false
}

variable "reloader_namespace" {
  description = "Reloader 命名空間"
  type        = string
  default     = "kube-system"
}

# HashiCorp Vault 變數
variable "enable_vault" {
  description = "是否啟用 HashiCorp Vault"
  type        = bool
  default     = true
}

variable "vault_namespace" {
  description = "Vault 安裝的 Kubernetes 命名空間"
  type        = string
  default     = "vault"
}

variable "vault_release_name" {
  description = "Vault Helm Release 名稱"
  type        = string
  default     = "vault"
}

variable "vault_chart_version" {
  description = "Vault Helm Chart 版本"
  type        = string
  default     = "0.27.0"
}

variable "vault_values_yaml" {
  description = "自定義 Vault 設定的 YAML 字串"
  type        = string
  default     = ""
}

variable "vault_ha_enabled" {
  description = "是否啟用 Vault 高可用模式"
  type        = bool
  default     = false
}

variable "vault_replicas" {
  description = "Vault 伺服器副本數量"
  type        = number
  default     = 3 # 高可用性配置，確保 PDB 能正常運作

  validation {
    condition     = var.vault_replicas >= 1 && var.vault_replicas <= 10
    error_message = "Vault 副本數量必須在 1 到 10 之間。"
  }

  validation {
    condition     = var.vault_replicas % 2 == 1
    error_message = "Vault 副本數量必須是奇數以確保高可用性的法定人數。"
  }
}

variable "vault_additional_set_values" {
  description = "額外的 Helm 設定值"
  type        = list(map(string))
  default     = []
}

# 使用 random_password 資源生成密碼，不再需要 db_password 變數

# ElastiCache Valkey 變數
variable "elasticache_identifier" {
  description = "ElastiCache 叢集識別碼"
  type        = string
  default     = "taipei-valkey"
}

variable "elasticache_node_type" {
  description = "ElastiCache 節點類型"
  type        = string
  default     = "cache.t4g.medium"

  validation {
    condition     = can(regex("^cache\\.[a-z][0-9][a-z]*\\.[a-z0-9]+$", var.elasticache_node_type))
    error_message = "ElastiCache 節點類型必須是有效的格式，例如：cache.t3.micro, cache.r5.large。"
  }
}

variable "elasticache_num_nodes" {
  description = "ElastiCache 節點數量"
  type        = number
  default     = 1

  validation {
    condition     = var.elasticache_num_nodes >= 1 && var.elasticache_num_nodes <= 20
    error_message = "ElastiCache 節點數量必須在 1 到 20 之間。"
  }
}

variable "elasticache_engine_version" {
  description = "Valkey 引擎版本"
  type        = string
  default     = "8.0"
}

variable "elasticache_parameter_group" {
  description = "ElastiCache 參數群組名稱"
  type        = string
  default     = "default.valkey8"
}
