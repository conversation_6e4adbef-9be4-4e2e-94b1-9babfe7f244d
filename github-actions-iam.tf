# GitHub Actions IAM 配置
# 建立 GitHub Actions 所需的 IAM resources

module "github_actions_iam" {
  count  = var.enable_github_actions_iam ? 1 : 0
  source = "./modules/github-actions-iam"

  # 基本配置
  role_name    = local.iam_role_names.github_actions
  github_org   = var.github_org
  github_repo  = var.github_repo
  cluster_name = var.cluster_name
  region       = var.region

  # Repository 和分支限制設定
  enable_repo_restrictions = var.github_enable_repo_restrictions
  github_branches          = var.github_allowed_branches

  # ECR 存取權限 - 允許存取所有 ECR repositories（可根據需要限制）
  ecr_repository_arns = var.github_actions_ecr_repository_arns

  # 額外的 assume role 權限（如果需要承擔其他 roles）
  additional_assume_role_arns = var.github_actions_additional_assume_role_arns

  # 額外的 managed policies
  additional_policy_arns = var.github_actions_additional_policy_arns

  # OIDC Provider 設定
  create_oidc_provider       = var.github_actions_create_oidc_provider
  existing_oidc_provider_arn = var.github_actions_existing_oidc_provider_arn

  # 標籤
  tags = merge(local.common_tags, {
    Name        = local.iam_role_names.github_actions
    Component   = "github-actions"
    Purpose     = "ci-cd"
    Description = "IAM role for GitHub Actions with ECR and EKS permissions"
  })
}
