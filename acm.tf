# 使用現有的 Route53 託管區域
data "aws_route53_zone" "existing" {
  zone_id = var.route53_zone_id
}

# 建立 ACM 憑證，涵蓋主要網域和萬用字元網域
module "acm_cert" {
  source = "./modules/acm"

  domain_name               = var.domain_name
  subject_alternative_names = var.subject_alternative_names
  route53_zone_id           = data.aws_route53_zone.existing.zone_id

  tags = merge(local.common_tags, {
    Name = "${local.service_names.acm}"
  })
}
