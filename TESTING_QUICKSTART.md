# 測試框架快速入門

## 5 分鐘快速開始

### 1. 驗證測試框架

```bash
# 驗證測試框架是否正確設置
./tests/scripts/validate.sh
```

### 2. 執行基本測試

```bash
# 執行快速測試（不需要 AWS 憑證）
make test-fast

# 查看所有可用命令
make help
```

### 3. 檢查工具安裝

```bash
# 檢查必要工具
make check-tools

# 自動安裝測試工具（可選）
make install-tools
```

## 常用測試命令

### 靜態分析

```bash
# 格式化代碼
make fmt

# 驗證語法
make validate

# 執行靜態分析
make test-static

# 執行 linting
make test-lint

# 執行安全掃描
make test-security
```

### 單元測試

```bash
# 執行所有單元測試（需要 Go）
make test-unit

# 執行特定模組測試
make test-unit-vpc
make test-unit-eks
```

### 整合測試

```bash
# ⚠️ 警告：會在 AWS 中創建實際資源，產生費用
make test-integration
```

## 測試配置

### 測試環境變數

```bash
# 設置 AWS 區域
export AWS_REGION=ap-east-2

# 設置 AWS Profile（可選）
export AWS_PROFILE=test

# 跳過資源清理（調試用）
export SKIP_TEARDOWN=true
```

### 配置文件

- `tests/fixtures/test.tfvars` - 標準測試配置
- `tests/fixtures/minimal.tfvars` - 最小測試配置

## 故障排除

### 常見問題

#### 1. Go 未安裝
```bash
# macOS
brew install go

# Ubuntu/Debian
sudo apt-get install golang-go
```

#### 2. AWS 憑證問題
```bash
# 檢查憑證
aws sts get-caller-identity

# 配置憑證
aws configure
```

#### 3. Terraform 初始化問題
```bash
# 清理並重新初始化
make clean
terraform init
```

### 調試技巧

```bash
# 詳細輸出
make test-static VERBOSE=1

# 保留測試資源
export SKIP_TEARDOWN=true
make test-unit
```

## 開發工作流程

### 1. 修改代碼前

```bash
# 執行快速測試確保基線正常
make test-fast
```

### 2. 修改代碼後

```bash
# 格式化代碼
make fmt

# 執行靜態分析
make test-static

# 執行相關單元測試
make test-unit
```

### 3. 提交前

```bash
# 執行完整測試套件
make test-all
```

## CI/CD 整合

### GitHub Actions

測試會在以下情況自動執行：
- Push to main/develop 分支
- 創建 Pull Request
- 手動觸發工作流程

### 本地 CI 模擬

```bash
# 模擬 CI 環境測試
make test-ci
```

## 進階用法

### 自定義測試

```bash
# 執行特定測試文件
cd tests
go test -v ./unit/vpc_test.go

# 執行特定測試函數
go test -run TestVPCModule ./unit/
```

### 效能測試

```bash
# 並行執行測試
go test -parallel 4 ./unit/...

# 基準測試
go test -bench=. ./unit/...
```

### 測試覆蓋率

```bash
# 生成覆蓋率報告
cd tests
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## 最佳實踐

### 1. 測試頻率

- **每次修改後**: 執行 `make test-fast`
- **提交前**: 執行 `make test-all`
- **發布前**: 執行完整測試包括整合測試

### 2. 成本控制

- 使用最小配置進行測試
- 及時清理測試資源
- 使用測試專用 AWS 帳戶

### 3. 安全考量

- 不在代碼中硬編碼憑證
- 使用 IAM 角色而非用戶
- 定期輪換測試憑證

## 相關文檔

- [完整測試框架文檔](TESTING_FRAMEWORK.md)
- [測試詳細說明](tests/README.md)
- [Terratest 官方文檔](https://terratest.gruntwork.io/)

## 獲取幫助

```bash
# 查看 Makefile 幫助
make help

# 驗證測試框架
./tests/scripts/validate.sh

# 清理測試環境
./tests/scripts/cleanup.sh
```

---

**提示**: 如果您是第一次使用，建議先執行 `./tests/scripts/validate.sh` 確保測試框架正確設置。
