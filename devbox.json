{"$schema": "https://raw.githubusercontent.com/jetpack-io/devbox/0.12.0/.schema/devbox.schema.json", "packages": ["terraform@1.5.7", "awscli2@2.15.0", "kubectl@1.28.4", "helm@3.13.3", "go@1.21.5", "nodejs@20.10.0", "python3@3.11.6", "jq@1.7", "yq-go@4.40.5", "curl@8.4.0", "git@2.42.1", "make@4.4.1", "direnv@2.32.3"], "shell": {"init_hook": ["echo '🚀 Devbox 環境已啟動'", "echo '📦 已安裝的工具:'", "echo '  - Terraform $(terraform version | head -n1 | cut -d\" \" -f2)'", "echo '  - AWS CLI $(aws --version | cut -d\" \" -f1 | cut -d\"/\" -f2)'", "echo '  - kubectl $(kubectl version --client --short 2>/dev/null | cut -d\" \" -f3)'", "echo '  - <PERSON><PERSON> $(helm version --short | cut -d\" \" -f1 | cut -d\":\" -f2)'", "echo '  - Go $(go version | cut -d\" \" -f3)'", "echo '  - Node.js $(node --version)'", "echo '  - Python $(python3 --version | cut -d\" \" -f2)'", "echo ''", "echo '💡 提示:'", "echo '  - 使用 direnv allow 來載入 .envrc 環境變數'", "echo '  - 執行 make help 查看可用的命令'", "echo '  - 執行 make setup 初始化測試環境'", "echo ''", "# 建立必要的目錄", "mkdir -p ~/.terraform.d/plugin-cache", "mkdir -p ~/.kube", "# 檢查 direnv 是否已允許", "if [ -f .envrc ] && ! direnv status | grep -q 'Found RC allowed true'; then", "  echo '⚠️  請執行 \"direnv allow\" 來載入環境變數'", "fi"], "scripts": {"setup": ["echo '🔧 設置開發環境...'", "# 複製範例配置檔案", "if [ ! -f terraform.tfvars ]; then", "  cp terraform.tfvars.example terraform.tfvars", "  echo '✅ 已建立 terraform.tfvars，請編輯以符合您的環境'", "fi", "if [ ! -f backend.conf ]; then", "  cp backend.conf.example backend.conf", "  echo '✅ 已建立 backend.conf，請編輯以符合您的環境'", "fi", "# 初始化 Go 模組 (用於測試)", "cd tests && go mod tidy && cd ..", "echo '✅ Go 測試模組已初始化'", "# 允許 direnv", "if [ -f .envrc ]; then", "  direnv allow", "  echo '✅ direnv 已允許載入環境變數'", "fi", "echo '🎉 開發環境設置完成！'"], "tf-init": ["echo '🔄 初始化 Terraform...'", "terraform init -backend-config=backend.conf"], "tf-plan": ["echo '📋 生成 Terraform 執行計劃...'", "terraform plan -var-file=terraform.tfvars"], "tf-apply": ["echo '🚀 應用 Terraform 配置...'", "terraform apply -var-file=terraform.tfvars"], "tf-destroy": ["echo '💥 銷毀 Terraform 資源...'", "terraform destroy -var-file=terraform.tfvars"], "test": ["echo '🧪 執行所有測試...'", "make test-all"], "test-static": ["echo '🔍 執行靜態分析...'", "make test-static"], "test-unit": ["echo '🧪 執行單元測試...'", "make test-unit"], "aws-login": ["echo '🔐 AWS SSO 登入...'", "aws sso login --profile ${AWS_PROFILE:-default}"], "k8s-config": ["echo '⚙️  更新 Kubernetes 配置...'", "aws eks update-kubeconfig --region ${AWS_REGION:-ap-east-2} --name ${TF_VAR_cluster_name:-longshun-tpe}"], "clean": ["echo '🧹 清理 Terraform 檔案...'", "make clean"]}}, "env": {"DEVBOX_TERRAFORM_VERSION": "1.5.7", "DEVBOX_AWS_CLI_VERSION": "2.15.0", "DEVBOX_KUBECTL_VERSION": "1.28.4", "DEVBOX_HELM_VERSION": "3.13.3", "DEVBOX_GO_VERSION": "1.21.5"}}