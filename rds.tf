module "rds" {
  source = "./modules/rds"

  # 使用標準化命名
  identifier        = local.service_names.rds
  instance_class    = var.db_instance_class
  allocated_storage = var.db_allocated_storage
  db_name           = var.db_name
  username          = var.db_username
  password          = random_password.db_password.result

  vpc_id                  = module.vpc.vpc_id
  subnet_ids              = module.vpc.public_subnets
  allowed_security_groups = [module.eks.cluster_security_group_id]
  allowed_cidr_blocks     = var.allowed_cidr_blocks

  tags = local.resource_specific_tags.rds_tags
}
