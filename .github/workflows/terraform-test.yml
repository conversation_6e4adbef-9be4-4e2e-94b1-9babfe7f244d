name: Terraform 測試

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  TF_VERSION: 1.5.0
  GO_VERSION: 1.21
  AWS_REGION: ap-east-2

jobs:
  # 靜態分析工作
  static-analysis:
    name: 靜態分析
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: ${{ env.TF_VERSION }}
        
    - name: Terraform 格式檢查
      run: terraform fmt -check -recursive
      
    - name: Terraform 初始化
      run: terraform init -backend=false
      
    - name: Terraform 驗證
      run: terraform validate
      
    - name: 設置 TFLint
      uses: terraform-linters/setup-tflint@v4
      with:
        tflint_version: latest
        
    - name: TFLint 初始化
      run: tflint --init
      
    - name: 執行 TFLint
      run: tflint --format compact
      
    - name: 設置 tfsec
      uses: aquasecurity/tfsec-action@v1.0.3
      with:
        soft_fail: true
        
  # 單元測試工作
  unit-tests:
    name: 單元測試
    runs-on: ubuntu-latest
    needs: static-analysis
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
        
    - name: 設置 Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: ${{ env.TF_VERSION }}
        terraform_wrapper: false
        
    - name: Go 模組快取
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('tests/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
          
    - name: 下載 Go 依賴
      working-directory: tests
      run: go mod download
      
    - name: 執行單元測試
      working-directory: tests
      run: go test -v -short ./unit/...
      
  # 安全掃描工作
  security-scan:
    name: 安全掃描
    runs-on: ubuntu-latest
    needs: static-analysis
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 執行 Checkov
      uses: bridgecrewio/checkov-action@master
      with:
        directory: .
        framework: terraform
        soft_fail: true
        output_format: sarif
        output_file_path: checkov-results.sarif
        
    - name: 上傳 Checkov 結果到 GitHub Security
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: checkov-results.sarif
        
  # 整合測試工作（僅在特定條件下執行）
  integration-tests:
    name: 整合測試
    runs-on: ubuntu-latest
    needs: [static-analysis, unit-tests]
    if: github.event_name == 'workflow_dispatch' || contains(github.event.head_commit.message, '[integration-test]')
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
        
    - name: 設置 Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: ${{ env.TF_VERSION }}
        terraform_wrapper: false
        
    - name: 配置 AWS 憑證
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
        
    - name: 執行整合測試
      working-directory: tests
      run: go test -v -timeout 45m ./integration/...
      env:
        AWS_REGION: ${{ env.AWS_REGION }}
        
  # 計劃生成工作
  terraform-plan:
    name: Terraform 計劃
    runs-on: ubuntu-latest
    needs: static-analysis
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout 代碼
      uses: actions/checkout@v4
      
    - name: 設置 Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: ${{ env.TF_VERSION }}
        
    - name: 配置 AWS 憑證
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
        
    - name: Terraform 初始化
      run: terraform init
      
    - name: Terraform 計劃
      run: terraform plan -var-file=tests/fixtures/test.tfvars -no-color
      continue-on-error: true
      
  # 測試結果摘要
  test-summary:
    name: 測試摘要
    runs-on: ubuntu-latest
    needs: [static-analysis, unit-tests, security-scan]
    if: always()
    
    steps:
    - name: 測試結果摘要
      run: |
        echo "## 測試結果摘要" >> $GITHUB_STEP_SUMMARY
        echo "| 測試類型 | 狀態 |" >> $GITHUB_STEP_SUMMARY
        echo "|---------|------|" >> $GITHUB_STEP_SUMMARY
        echo "| 靜態分析 | ${{ needs.static-analysis.result == 'success' && '✅ 通過' || '❌ 失敗' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| 單元測試 | ${{ needs.unit-tests.result == 'success' && '✅ 通過' || '❌ 失敗' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| 安全掃描 | ${{ needs.security-scan.result == 'success' && '✅ 通過' || '⚠️ 有警告' }} |" >> $GITHUB_STEP_SUMMARY
        
        if [[ "${{ needs.static-analysis.result }}" == "success" && "${{ needs.unit-tests.result }}" == "success" ]]; then
          echo "🎉 所有核心測試都通過了！" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ 有測試失敗，請檢查詳細日誌" >> $GITHUB_STEP_SUMMARY
        fi
