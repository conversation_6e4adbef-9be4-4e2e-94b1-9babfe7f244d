# EKS Node Group 更新問題解決方案

## 問題診斷

### 問題現象
- Terraform apply 在 EKS node group 更新時卡住超過 30 分鐘
- 錯誤訊息：`PodEvictionFailure: Reached max retries while trying to evict pods from nodes`

### 根本原因
1. **Vault PodDisruptionBudget 限制**：Vault 的 PDB 設定為 `maxUnavailable: 1`，但只有 1 個 replica，導致 `ALLOWED DISRUPTIONS: 0`
2. **標籤變更觸發 Rolling Update**：Terraform 的標籤變更觸發了不必要的 node group rolling update

### 當前狀態
```bash
# Node Group 狀態
aws eks describe-nodegroup --cluster-name longshun-tpe --nodegroup-name default_node_group-20250712083918739300000006 --region ap-east-2

# 問題節點上的 Pods
kubectl get pods --all-namespaces -o wide | grep ip-172-11-3-194

# PodDisruptionBudgets
kubectl get pdb --all-namespaces
```

## 解決方案

### 方案 1：臨時調整 Vault PDB（推薦）

1. **臨時移除 Vault PDB**：
```bash
kubectl patch pdb vault -n vault --type merge -p '{"spec":{"maxUnavailable":"100%"}}'
```

2. **重新執行 Terraform Apply**：
```bash
terraform apply -auto-approve
```

3. **恢復 Vault PDB**（apply 完成後）：
```bash
kubectl patch pdb vault -n vault --type merge -p '{"spec":{"maxUnavailable":"1"}}'
```

### 方案 2：增加 Vault Replicas

1. **臨時增加 Vault replicas**：
```bash
kubectl scale statefulset vault -n vault --replicas=2
```

2. **等待新 pod 就緒**：
```bash
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=vault -n vault --timeout=300s
```

3. **重新執行 Terraform Apply**：
```bash
terraform apply -auto-approve
```

4. **恢復 Vault replicas**（apply 完成後）：
```bash
kubectl scale statefulset vault -n vault --replicas=1
```

### 方案 3：強制完成 Node Group 更新

1. **取消當前更新**：
```bash
aws eks cancel-nodegroup-update --cluster-name longshun-tpe --nodegroup-name default_node_group-20250712083918739300000006 --region ap-east-2
```

2. **等待取消完成**：
```bash
aws eks wait nodegroup-active --cluster-name longshun-tpe --nodegroup-name default_node_group-20250712083918739300000006 --region ap-east-2
```

3. **重新執行 Terraform Apply**：
```bash
terraform apply -auto-approve
```

## 預防措施

### 1. 改善 Vault 配置

在 `modules/eks-addons/vault/main.tf` 中調整 PDB 設定：

```hcl
# 在 Vault Helm values 中加入
values = [
  yamlencode({
    server = {
      ha = {
        enabled = true
        replicas = 2  # 增加 replicas
      }
      disruption = {
        enabled = true
        maxUnavailable = 1  # 允許 1 個 pod 不可用
      }
    }
  })
]
```

### 2. 避免不必要的 Node Group 更新

在 EKS node group 配置中加入 `ignore_changes`：

```hcl
resource "aws_eks_node_group" "this" {
  # ... 其他配置

  lifecycle {
    ignore_changes = [
      tags,
      tags_all
    ]
  }
}
```

### 3. 改善 Node Group 更新策略

```hcl
update_config = {
  max_unavailable_percentage = 25  # 降低同時更新的節點比例
}
```

## 執行步驟

### 立即解決（方案 1）

```bash
# 1. 臨時調整 Vault PDB
kubectl patch pdb vault -n vault --type merge -p '{"spec":{"maxUnavailable":"100%"}}'

# 2. 重新執行 Terraform
terraform apply -auto-approve

# 3. 監控進度
watch -n 30 'aws eks describe-nodegroup --cluster-name longshun-tpe --nodegroup-name default_node_group-20250712083918739300000006 --region ap-east-2 --query "nodegroup.status"'

# 4. 完成後恢復 PDB
kubectl patch pdb vault -n vault --type merge -p '{"spec":{"maxUnavailable":"1"}}'
```

### 長期改善

1. **更新 Vault 配置**以支援 HA
2. **加入 lifecycle ignore_changes**避免不必要的更新
3. **改善 monitoring**以提早發現類似問題

## 監控命令

```bash
# 檢查 Node Group 狀態
aws eks describe-nodegroup --cluster-name longshun-tpe --nodegroup-name default_node_group-20250712083918739300000006 --region ap-east-2 --query "nodegroup.status"

# 檢查節點狀態
kubectl get nodes

# 檢查 PDB 狀態
kubectl get pdb --all-namespaces

# 檢查 Vault 狀態
kubectl get pods -n vault
kubectl get pvc -n vault
```

## 風險評估

- **低風險**：方案 1（臨時調整 PDB）
- **中風險**：方案 2（增加 replicas）
- **高風險**：方案 3（取消更新）

建議使用方案 1，因為它最安全且最快速。
