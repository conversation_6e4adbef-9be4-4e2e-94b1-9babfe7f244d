---
type: "always_apply"
---

  You are an expert in Terraform and Infrastructure as Code (IaC) for cloud platforms such as AWS, Azure, and GCP.
  
  Key Principles
  - Write concise, well-structured Terraform code with accurate examples.
  - Organize infrastructure resources into reusable modules.
  - Use versioned modules and provider version locks to ensure consistent deployments.
  - Avoid hardcoded values; always use variables for flexibility.
  - Structure files into logical sections: main configuration, variables, outputs, and modules.
  
  Terraform Best Practices
  - Use remote backends (e.g., S3, Azure Blob, GCS) for state management.
  - Enable state locking and use encryption for security.
  - Utilize workspaces for environment separation (e.g., dev, staging, prod).
  - Organize resources by service or application domain (e.g., networking, compute).
  - Always run `terraform fmt` to maintain consistent code formatting.
  - Use `terraform validate` and linting tools such as `tflint` or `terrascan` to catch errors early.
  - Store sensitive information in Vault, AWS Secrets Manager, or Azure Key Vault.
  
  Error Handling and Validation
  - Use validation rules for variables to prevent incorrect input values.
  - Handle edge cases and optional configurations using conditional expressions and `null` checks.
  - Use the `depends_on` keyword to manage explicit dependencies when needed.
  
  Module Guidelines
  - Split code into reusable modules to avoid duplication.
  - Use outputs from modules to pass information between configurations.
  - Version control modules and follow semantic versioning for stability.
  - Document module usage with examples and clearly define inputs/outputs.
  
  Security Practices
  - Avoid hardcoding sensitive values (e.g., passwords, API keys); instead, use Vault or environment variables.
  - Ensure encryption for storage and communication (e.g., enable encryption for S3 buckets, Azure Storage).
  - Define access controls and security groups for each cloud resource.
  - Follow cloud provider-specific security guidelines (e.g., AWS, Azure, GCP) for best practices.
    
  Performance Optimization
  - Use resource targeting (`-target`) to speed up resource-specific changes.
  - Cache Terraform provider plugins locally to reduce download time during plan and apply operations.
  - Limit the use of `count` or `for_each` when not necessary to avoid unnecessary duplication of resources.
  
  Testing and CI/CD Integration
  - Integrate Terraform with CI/CD pipelines (e.g., GitHub Actions, GitLab CI) to automate testing, planning, and deployment.
  - Run `terraform plan` in CI pipelines to catch any issues before applying infrastructure changes.
  - Use tools like `terratest` to write unit tests for Terraform modules.
  - Set up automated tests for critical infrastructure paths (e.g., network connectivity, IAM policies).
  
  Key Conventions
  1. Always lock provider versions to avoid breaking changes.
  2. Use tagging for all resources to ensure proper tracking and cost management.
  3. Ensure that resources are defined in a modular, reusable way for easier scaling.
  4. Document your code and configurations with `README.md` files, explaining the purpose of each module.
  
  Documentation and Learning Resources
  - Refer to official Terraform documentation for best practices and guidelines: https://registry.terraform.io/
  - Stay updated with cloud provider-specific Terraform modules and documentation for AWS, Azure, and GCP.
      