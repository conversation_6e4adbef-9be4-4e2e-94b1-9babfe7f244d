# Devbox 和 direnv 設定指南

本專案使用 [devbox](https://www.jetpack.io/devbox) 和 [direnv](https://direnv.net/) 來管理開發環境和環境變數。

## 快速開始

### 1. 安裝必要工具

```bash
# 安裝 devbox
curl -fsSL https://get.jetpack.io/devbox | bash

# 安裝 direnv (macOS)
brew install direnv

# 安裝 direnv (Ubuntu/Debian)
sudo apt install direnv

# 安裝 direnv (其他系統)
# 請參考: https://direnv.net/docs/installation.html
```

### 2. 設定 shell 整合

將以下內容加入您的 shell 配置檔案：

**Bash (~/.bashrc)**
```bash
eval "$(direnv hook bash)"
```

**Zsh (~/.zshrc)**
```bash
eval "$(direnv hook zsh)"
```

**Fish (~/.config/fish/config.fish)**
```fish
direnv hook fish | source
```

### 3. 初始化專案環境

```bash
# 進入專案目錄
cd /path/to/infra-taipei

# 啟動 devbox 環境
devbox shell

# 設定開發環境
devbox run setup

# 允許 direnv 載入環境變數
direnv allow
```

## 配置檔案說明

### devbox.json
- 定義開發環境所需的工具和版本
- 包含常用的 Terraform、AWS CLI、kubectl 等工具
- 提供便利的腳本命令

### .envrc
- 包含專案特定的環境變數
- 設定 AWS 認證和 Terraform 變數
- 自動載入開發環境配置

### .envrc.example
- 環境變數配置範本
- 複製並修改為實際的 `.envrc` 檔案

## 常用命令

### Devbox 命令

```bash
# 進入 devbox 環境
devbox shell

# 執行設定腳本
devbox run setup

# Terraform 操作
devbox run tf-init     # 初始化 Terraform
devbox run tf-plan     # 生成執行計劃
devbox run tf-apply    # 應用配置
devbox run tf-destroy  # 銷毀資源

# 測試命令
devbox run test        # 執行所有測試
devbox run test-static # 執行靜態分析
devbox run test-unit   # 執行單元測試

# AWS 和 Kubernetes
devbox run aws-login   # AWS SSO 登入
devbox run k8s-config  # 更新 kubectl 配置

# 清理
devbox run clean       # 清理 Terraform 檔案
```

### 直接使用工具

在 devbox shell 中，您可以直接使用所有已安裝的工具：

```bash
# Terraform
terraform init -backend-config=backend.conf
terraform plan -var-file=terraform.tfvars
terraform apply -var-file=terraform.tfvars

# AWS CLI
aws sts get-caller-identity
aws eks list-clusters

# kubectl
kubectl get nodes
kubectl get pods --all-namespaces

# 測試
make test-static
make test-unit
make test-all
```

## 環境變數配置

### AWS 認證設定

推薦的認證方式（按優先順序）：

1. **AWS SSO** (企業環境推薦)
```bash
export AWS_PROFILE="your-sso-profile"
```

2. **AWS CLI Profile** (個人開發推薦)
```bash
export AWS_PROFILE="your-profile-name"
```

3. **環境變數** (CI/CD 環境)
```bash
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
```

### Terraform 變數

主要的 Terraform 變數都可以透過環境變數設定：

```bash
export TF_VAR_region="ap-east-2"
export TF_VAR_environment="dev"
export TF_VAR_cluster_name="your-cluster"
export TF_VAR_vpc_cidr="10.0.0.0/16"
```

## 安全性最佳實踐

1. **不要提交敏感資訊**
   - `.envrc` 已加入 `.gitignore`
   - 使用 `.envrc.example` 作為範本

2. **使用 AWS Profiles**
   - 避免在環境變數中硬編碼 AWS 金鑰
   - 使用 AWS CLI profiles 或 SSO

3. **定期輪換金鑰**
   - 定期更新 AWS 存取金鑰
   - 使用臨時憑證

4. **環境隔離**
   - 為不同環境使用不同的配置
   - 使用 Terraform workspaces

## 故障排除

### direnv 未載入環境變數
```bash
# 檢查 direnv 狀態
direnv status

# 重新載入
direnv reload

# 允許載入
direnv allow
```

### devbox 工具版本問題
```bash
# 更新 devbox
devbox version update

# 重新安裝套件
devbox run setup
```

### AWS 認證問題
```bash
# 檢查 AWS 認證
aws sts get-caller-identity

# 檢查 AWS 配置
aws configure list

# AWS SSO 登入
aws sso login --profile your-profile
```

### Terraform 初始化問題
```bash
# 清理 Terraform 狀態
rm -rf .terraform/
rm .terraform.lock.hcl

# 重新初始化
terraform init -backend-config=backend.conf
```

## 更多資源

- [Devbox 文檔](https://www.jetpack.io/devbox/docs/)
- [direnv 文檔](https://direnv.net/)
- [Terraform 文檔](https://www.terraform.io/docs/)
- [AWS CLI 文檔](https://docs.aws.amazon.com/cli/)
