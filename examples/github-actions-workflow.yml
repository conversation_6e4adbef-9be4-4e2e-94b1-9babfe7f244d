# GitHub Actions Workflow 範例
# 此檔案展示如何使用 longshun-prod-github-actions IAM role 進行 CI/CD

name: Deploy Application to EKS

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

# 必要的權限設定
permissions:
  id-token: write   # 用於 OIDC 認證
  contents: read    # 讀取 repository 內容

env:
  AWS_REGION: ap-east-2
  EKS_CLUSTER_NAME: longshun-tpe
  ECR_REPOSITORY: your-app-name

jobs:
  # 建置和測試階段
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Run tests
        run: |
          # 在這裡執行您的測試
          echo "Running tests..."
          # npm test, go test, pytest, etc.

  # 部署階段（只在 main 和 develop 分支）
  deploy:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          role-session-name: GitHubActions-${{ github.run_id }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          # 建置 Docker image
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:latest .
          
          # 推送到 ECR
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
          
          # 輸出 image URI 供後續步驟使用
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region ${{ env.AWS_REGION }} --name ${{ env.EKS_CLUSTER_NAME }}

      - name: Deploy to EKS
        env:
          IMAGE_URI: ${{ steps.build-image.outputs.image }}
        run: |
          # 設定部署環境
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            NAMESPACE="production"
            DEPLOYMENT_NAME="app-prod"
          else
            NAMESPACE="staging"
            DEPLOYMENT_NAME="app-staging"
          fi
          
          # 確保 namespace 存在
          kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
          
          # 更新 deployment 的 image
          kubectl set image deployment/$DEPLOYMENT_NAME app=$IMAGE_URI -n $NAMESPACE
          
          # 等待 rollout 完成
          kubectl rollout status deployment/$DEPLOYMENT_NAME -n $NAMESPACE --timeout=300s
          
          # 驗證部署
          kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT_NAME

      - name: Run post-deployment tests
        run: |
          # 在這裡執行部署後的測試
          echo "Running post-deployment tests..."
          # 健康檢查、整合測試等

  # 安全掃描（可選）
  security-scan:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          role-session-name: GitHubActions-SecurityScan-${{ github.run_id }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build image for scanning
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: scan-${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:scan-${{ github.sha }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
