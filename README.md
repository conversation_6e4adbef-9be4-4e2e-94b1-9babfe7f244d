# infra-taipei

## 專案簡介

本專案以 Terraform 管理 AWS 雲端基礎設施，涵蓋 VPC、EKS、RDS、ElastiCache、Route53、KMS、ACM、Secrets 及多項 EKS Addons（如 ExternalDNS、Vault、Cluster Autoscaler 等），並依循最佳實踐進行模組化設計，適合多環境（dev/staging/prod）自動化部署。

---

## 架構總覽

- **VPC**：自動建立多 AZ、私有/公有子網，支援 VPC Endpoint。
- **EKS**：Kubernetes 叢集，支援多節點群組與 IAM 整合。
- **RDS**：PostgreSQL 資料庫，密碼自動產生並儲存於 Secrets Manager。
- **ElastiCache**：Valkey 快取叢集，部署於私有子網。
- **Route53**：DNS 託管區域與記錄。
- **KMS**：金鑰管理，供 Vault 解封與加密使用。
- **ACM**：自動申請 SSL 憑證。
- **Secrets**：集中管理敏感資訊。
- **EKS Addons**：
  - AWS Load Balancer Controller
  - Cluster Autoscaler
  - ExternalDNS
  - External Secrets
  - Metrics Server
  - Reloader
  - Vault（高可用模式）

---

## 目錄結構

```
infra-taipei/
  ├── main.tf                # 主組態，引用各模組
  ├── variables.tf           # 全域變數定義
  ├── outputs.tf             # 全域輸出
  ├── terraform.tf           # backend 與 provider 設定
  ├── modules/               # 各服務模組
  │   ├── vpc/               # VPC 子模組
  │   ├── eks/               # EKS 子模組
  │   ├── rds/               # RDS 子模組
  │   ├── elasticache/       # ElastiCache 子模組
  │   ├── route53/           # Route53 子模組
  │   ├── kms/               # KMS 子模組
  │   ├── acm/               # ACM 子模組
  │   ├── secrets/           # Secrets 子模組
  │   ├── eks-addons/        # EKS 附加元件（多層子模組）
  │   └── github-actions-iam/ # GitHub Actions IAM 子模組
  └── ...
```

---

## 主要變數（部分範例）

| 變數名稱                | 說明                       | 預設值/型別         |
|-------------------------|----------------------------|---------------------|
| region                  | AWS 區域                   | ap-east-2 (string)  |
| vpc_cidr                | VPC CIDR 區塊              | **********/16       |
| availability_zones      | 可用區域列表               | [ap-east-2a,...]    |
| cluster_name            | EKS 叢集名稱               | taipei-eks-cluster  |
| node_groups             | EKS 節點群組設定           | map/object          |
| db_identifier           | RDS 識別碼                  | taipei-postgres     |
| db_username             | RDS 管理員帳號             | postgres            |
| elasticache_identifier  | ElastiCache 識別碼         | -                   |
| enable_github_actions_iam | 是否啟用 GitHub Actions IAM | false (bool)        |
| github_org              | GitHub 組織名稱            | string              |
| github_repo             | GitHub repository 名稱     | string              |
| tags                    | 資源標籤                   | map(string)         |

> 更多變數請參考 `variables.tf` 及各模組內 `variables.tf`。

---

## 主要 Outputs（部分範例）

| 輸出名稱                        | 說明                                 |
|----------------------------------|--------------------------------------|
| vpc_id                          | VPC ID                               |
| eks_cluster_id                  | EKS 叢集 ID                          |
| eks_cluster_endpoint            | EKS 叢集 API 端點                    |
| db_instance_endpoint            | RDS 端點                             |
| db_password_secret_arn          | 資料庫密碼的 Secrets Manager ARN      |
| vault_root_token_secret_name     | Vault root token 的 Secrets 名稱      |
| vault_recovery_keys_secret_name  | Vault recovery keys 的 Secrets 名稱   |
| github_actions_role_arn         | GitHub Actions IAM role ARN          |
| github_oidc_provider_arn        | GitHub OIDC provider ARN             |

> 更多 outputs 請參考 `outputs.tf` 及各模組內 `outputs.tf`。

---

## 部署前置需求

- Terraform >= 1.0.0
- AWS CLI 已設定憑證與預設區域
- 有權限操作 S3、IAM、EKS、RDS、ElastiCache、Route53、KMS、Secrets Manager 等資源

---

## 快速開始

```sh
# 1. 複製並配置變數檔案
cp terraform.tfvars.example terraform.tfvars
# 編輯 terraform.tfvars 以符合您的環境

# 2. 複製並配置 backend 檔案（可選）
cp backend.conf.example backend.conf
# 編輯 backend.conf 以符合您的 S3 bucket 和 DynamoDB 表設定

# 3. 初始化 Terraform
terraform init
# 或使用自訂 backend 配置：
# terraform init -backend-config=backend.conf

# 4. 格式化檔案
terraform fmt

# 5. 檢查語法
terraform validate

# 6. 預覽變更
terraform plan

# 7. 套用變更
terraform apply
```

---

## State Locking 配置

此專案已配置 Terraform State Locking 以確保多人協作時的狀態文件安全性：

### 自動配置
- **DynamoDB 表**：自動創建 `{cluster_name}-terraform-state-lock` 表
- **加密**：啟用伺服器端加密和時間點恢復
- **防護**：設定 `prevent_destroy` 生命週期規則

### 手動配置（如需要）
如果您需要使用現有的 DynamoDB 表或自訂配置：

1. 在 `terraform.tfvars` 中設定：
   ```hcl
   terraform_state_lock_table = "your-existing-table-name"
   ```

2. 或在 `backend.conf` 中指定：
   ```hcl
   dynamodb_table = "your-existing-table-name"
   ```

### 注意事項
- DynamoDB 表必須有 `LockID` 作為 hash key (字串類型)
- 建議使用 PAY_PER_REQUEST 計費模式以節省成本
- 表名必須在 AWS 區域內唯一

---

## 測試框架

本專案包含完整的測試框架，支援多層次的自動化測試：

### 快速開始測試

```sh
# 驗證測試框架
./tests/scripts/validate.sh

# 執行快速測試
make test-fast

# 執行所有測試（不包含整合測試）
make test-all

# 查看所有測試命令
make help
```

### 測試類型

- **靜態分析**: Terraform 格式檢查、語法驗證、linting
- **單元測試**: 使用 Terratest 測試各個模組
- **整合測試**: 完整的基礎設施部署測試
- **安全掃描**: tfsec、checkov 安全性檢查

### CI/CD 整合

- GitHub Actions 自動化測試工作流程
- Pull Request 自動觸發測試
- 安全掃描結果整合到 GitHub Security

詳細資訊請參考 [測試框架文檔](TESTING_FRAMEWORK.md) 和 [tests/README.md](tests/README.md)。

---

## GitHub Actions CI/CD 整合

本專案支援 GitHub Actions 的 CI/CD 整合，提供安全的 OIDC 認證和必要的 AWS 權限：

### 快速設定

1. **啟用 GitHub Actions IAM**：
   ```hcl
   enable_github_actions_iam = true
   github_org = "your-org"
   github_repo = "your-repo"
   ```

2. **部署基礎設施**：
   ```bash
   terraform apply
   ```

3. **設定 GitHub Secrets**：
   - `AWS_ROLE_ARN`: 使用 `terraform output github_actions_role_arn` 取得

詳細設定請參考 [GitHub Actions 設定指南](GITHUB_ACTIONS_SETUP.md)。

---

## 最佳實踐與安全建議

- **狀態管理**：已使用 S3 遠端 backend 並啟用加密與 state locking。
- **Provider 鎖定**：所有 provider 皆有明確版本限制。
- **模組化設計**：所有資源皆以模組方式拆分，便於重用與維護。
- **敏感資訊管理**：密碼等敏感資訊皆存放於 AWS Secrets Manager。
- **標籤策略**：所有資源皆支援自訂標籤，便於成本控管與追蹤。
- **測試驅動**：完整的測試框架確保代碼品質和部署安全性。
- **CI/CD 整合**：自動化測試和部署流程。
- **安全性**：建議搭配 AWS IAM 最小權限原則、KMS 加密、VPC 私有子網等安全措施。

---

## 參考資源

- [Terraform 官方文件](https://registry.terraform.io/)
- [AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)
- [Kubernetes Provider](https://registry.terraform.io/providers/hashicorp/kubernetes/latest/docs)
- [Helm Provider](https://registry.terraform.io/providers/hashicorp/helm/latest/docs)

---

如需自訂模組或進階用法，請參考 `modules/` 目錄下各子模組的 `variables.tf` 與 `outputs.tf`，或聯絡專案維護者。 