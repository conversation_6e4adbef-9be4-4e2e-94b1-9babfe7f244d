# 不重複定義 AWS Provider，而是使用別名
provider "aws" {
  alias                  = "addons"
  region                 = var.region
  skip_region_validation = true
}

# EKS 附加元件特定的設置 - 使用 locals.tf 中集中管理的配置

# 為附加元件設定專用的 Kubernetes provider
provider "kubernetes" {
  alias                  = "eks_addons"
  host                   = module.eks.cluster_endpoint
  cluster_ca_certificate = base64decode(module.eks.certificate_authority_data)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    args        = ["eks", "get-token", "--cluster-name", local.addons_eks_cluster_name, "--region", var.region]
    command     = "aws"
  }
}

provider "helm" {
  alias = "eks_addons"

  kubernetes {
    host                   = module.eks.cluster_endpoint
    cluster_ca_certificate = base64decode(module.eks.certificate_authority_data)

    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      args        = ["eks", "get-token", "--cluster-name", local.addons_eks_cluster_name, "--region", var.region]
      command     = "aws"
    }
  }
}

# 部署 EKS 附加元件
module "eks_addons" {
  source                  = "./modules/eks-addons"
  cluster_name            = local.addons_eks_cluster_name
  oidc_provider_arn       = module.eks.oidc_provider_arn
  region                  = var.region
  vpc_id                  = module.vpc.vpc_id
  vault_awskms_key_id     = module.kms.kms_key_id
  cluster_endpoint        = module.eks.cluster_endpoint
  cluster_ca_certificate  = module.eks.certificate_authority_data
  eks_oidc_provider_url   = module.eks.oidc_provider_url
  cluster_oidc_issuer_url = module.eks.oidc_provider_url
  eks_node_role_arns      = module.eks.node_role_arns

  # AWS Load Balancer Controller 設定
  enable_aws_load_balancer_controller        = var.enable_aws_load_balancer_controller
  aws_load_balancer_controller_chart_version = local.helm_chart_versions.aws_load_balancer_controller

  # Cluster Autoscaler 設定
  enable_cluster_autoscaler        = var.enable_cluster_autoscaler
  cluster_autoscaler_chart_version = local.helm_chart_versions.cluster_autoscaler

  # External DNS 設定
  enable_external_dns               = var.enable_external_dns
  external_dns_chart_version        = local.helm_chart_versions.external_dns
  external_dns_domain_filters       = var.external_dns_domain_filters
  external_dns_namespace            = var.external_dns_namespace
  external_dns_create_namespace     = var.external_dns_create_namespace
  external_dns_service_account_name = var.external_dns_service_account_name
  external_dns_policy               = var.external_dns_policy
  external_dns_hosted_zone_arns     = var.external_dns_hosted_zone_arns
  external_dns_zone_type            = var.external_dns_zone_type
  external_dns_txt_owner_id         = var.external_dns_txt_owner_id
  external_dns_txt_prefix           = var.external_dns_txt_prefix

  # External Secrets 設定
  enable_external_secrets           = var.enable_external_secrets
  external_secrets_chart_version    = local.helm_chart_versions.external_secrets
  external_secrets_create_namespace = var.external_secrets_create_namespace
  external_secrets_namespace        = var.external_secrets_namespace

  # Metrics Server 設定
  enable_metrics_server           = var.enable_metrics_server
  metrics_server_addon_version    = var.metrics_server_addon_version
  metrics_server_create_namespace = var.metrics_server_create_namespace
  metrics_server_namespace        = var.metrics_server_namespace

  # Reloader 設定
  enable_reloader           = var.enable_reloader
  reloader_chart_version    = local.helm_chart_versions.reloader
  reloader_create_namespace = var.reloader_create_namespace
  reloader_namespace        = var.reloader_namespace

  # Vault 設定
  enable_vault          = true
  vault_chart_version   = local.helm_chart_versions.vault
  vault_namespace       = "vault"
  vault_replicas        = 3 # 高可用性配置
  private_subnet_ids    = module.vpc.private_subnets
  vault_certificate_arn = module.acm_cert.certificate_validation_arn

  # 使用 ACM 資格及 ALB 設定
  vault_additional_set_values = [
    {
      "name"  = "server.ingress.annotations.alb.ingress.kubernetes.io/certificate-arn"
      "value" = module.acm_cert.certificate_validation_arn
      "type"  = "string"
    },
    {
      "name"  = "server.ingress.annotations.alb\\.ingress\\.kubernetes\\.io/listen-ports\\.http"
      "value" = "80"
      "type"  = "string"
    },
    {
      "name"  = "server.ingress.annotations.alb\\.ingress\\.kubernetes\\.io/listen-ports\\.https"
      "value" = "443"
      "type"  = "string"
    },
    {
      "name"  = "server.ingress.annotations.alb\\.ingress\\.kubernetes\\.io/load-balancer-attributes"
      "value" = "waf.fail_open.enabled=true"
      "type"  = "string"
    },
    {
      "name"  = "server.ingress.annotations.alb\\.ingress\\.kubernetes\\.io/delete-load-balancer-on-cleanup"
      "value" = "true"
      "type"  = "string"
    },
    {
      "name"  = "server.ingress.annotations.alb\\.ingress\\.kubernetes\\.io/remove-http-redirect"
      "value" = "true"
      "type"  = "string"
    },
    {
      "name"  = "server.ingress.annotations.alb\\.ingress\\.kubernetes\\.io/remove-security-group-rule"
      "value" = "true"
      "type"  = "string"
    },
    {
      "name"  = "server.ingress.annotations.external-dns\\.alpha\\.kubernetes\\.io/hostname"
      "value" = "vault.longshun.io"
      "type"  = "string"
    }
  ]

  tags = local.common_tags

  # 使用專用的 provider
  providers = {
    kubernetes = kubernetes.eks_addons
    helm       = helm.eks_addons
  }

  # 確保 EKS 已經完全部署，包括所有節點群組和附加元件
  depends_on = [
    module.eks,
    module.eks.eks_managed_node_groups,
    module.eks.fargate_profiles,
    module.eks.aws_eks_addon
  ]
}
