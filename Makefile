# ============================================================================
# Terraform 測試框架 Makefile
# ============================================================================

.PHONY: help test test-unit test-integration test-static test-security test-all
.PHONY: fmt validate plan apply destroy clean setup
.PHONY: install-tools check-tools

# 預設目標
.DEFAULT_GOAL := help

# 變數定義
TERRAFORM_DIR := .
TESTS_DIR := tests
GO_TEST_TIMEOUT := 30m
AWS_REGION := ap-east-2

# 顏色定義
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

# ============================================================================
# 幫助和資訊
# ============================================================================

help: ## 顯示此幫助訊息
	@echo "$(BLUE)Terraform 測試框架$(NC)"
	@echo "$(BLUE)==================$(NC)"
	@echo ""
	@echo "$(YELLOW)可用的命令：$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)範例：$(NC)"
	@echo "  make test-static    # 執行靜態分析"
	@echo "  make test-unit      # 執行單元測試"
	@echo "  make test-all       # 執行所有測試"

# ============================================================================
# 工具安裝和檢查
# ============================================================================

install-tools: ## 安裝所需的測試工具
	@echo "$(BLUE)安裝測試工具...$(NC)"
	@command -v terraform >/dev/null 2>&1 || { echo "$(RED)請先安裝 Terraform$(NC)"; exit 1; }
	@command -v go >/dev/null 2>&1 || { echo "$(RED)請先安裝 Go$(NC)"; exit 1; }
	@echo "$(GREEN)安裝 tflint...$(NC)"
	@curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash || true
	@echo "$(GREEN)安裝 tfsec...$(NC)"
	@go install github.com/aquasecurity/tfsec/cmd/tfsec@latest
	@echo "$(GREEN)安裝 checkov...$(NC)"
	@pip3 install checkov || echo "$(YELLOW)請手動安裝 checkov: pip3 install checkov$(NC)"
	@echo "$(GREEN)安裝 terraform-docs...$(NC)"
	@go install github.com/terraform-docs/terraform-docs@latest
	@echo "$(GREEN)工具安裝完成$(NC)"

check-tools: ## 檢查所需工具是否已安裝
	@echo "$(BLUE)檢查工具...$(NC)"
	@command -v terraform >/dev/null 2>&1 && echo "$(GREEN)✓ Terraform$(NC)" || echo "$(RED)✗ Terraform$(NC)"
	@command -v go >/dev/null 2>&1 && echo "$(GREEN)✓ Go$(NC)" || echo "$(RED)✗ Go$(NC)"
	@command -v tflint >/dev/null 2>&1 && echo "$(GREEN)✓ tflint$(NC)" || echo "$(YELLOW)○ tflint (可選)$(NC)"
	@command -v tfsec >/dev/null 2>&1 && echo "$(GREEN)✓ tfsec$(NC)" || echo "$(YELLOW)○ tfsec (可選)$(NC)"
	@command -v checkov >/dev/null 2>&1 && echo "$(GREEN)✓ checkov$(NC)" || echo "$(YELLOW)○ checkov (可選)$(NC)"

# ============================================================================
# Terraform 基本操作
# ============================================================================

fmt: ## 格式化 Terraform 代碼
	@echo "$(BLUE)格式化 Terraform 代碼...$(NC)"
	@terraform fmt -recursive $(TERRAFORM_DIR)
	@echo "$(GREEN)格式化完成$(NC)"

validate: ## 驗證 Terraform 配置
	@echo "$(BLUE)驗證 Terraform 配置...$(NC)"
	@terraform init -backend=false
	@terraform validate
	@echo "$(GREEN)驗證通過$(NC)"

plan: ## 生成 Terraform 執行計劃
	@echo "$(BLUE)生成執行計劃...$(NC)"
	@terraform plan -var-file=tests/fixtures/test.tfvars -out=terraform.tfplan
	@echo "$(GREEN)計劃生成完成$(NC)"

# ============================================================================
# 靜態測試
# ============================================================================

test-static: fmt validate ## 執行靜態分析測試
	@echo "$(BLUE)執行靜態分析...$(NC)"
	@echo "$(YELLOW)1. Terraform 格式檢查$(NC)"
	@terraform fmt -check -recursive $(TERRAFORM_DIR) || (echo "$(RED)格式檢查失敗$(NC)" && exit 1)
	@echo "$(YELLOW)2. Terraform 語法驗證$(NC)"
	@terraform init -backend=false
	@terraform validate
	@echo "$(GREEN)靜態分析完成$(NC)"

test-lint: ## 執行 linting 檢查
	@echo "$(BLUE)執行 linting 檢查...$(NC)"
	@if command -v tflint >/dev/null 2>&1; then \
		echo "$(YELLOW)執行 tflint...$(NC)"; \
		tflint --init; \
		tflint; \
	else \
		echo "$(YELLOW)tflint 未安裝，跳過$(NC)"; \
	fi

test-security: ## 執行安全性掃描
	@echo "$(BLUE)執行安全性掃描...$(NC)"
	@if command -v tfsec >/dev/null 2>&1; then \
		echo "$(YELLOW)執行 tfsec...$(NC)"; \
		tfsec $(TERRAFORM_DIR); \
	else \
		echo "$(YELLOW)tfsec 未安裝，跳過$(NC)"; \
	fi
	@if command -v checkov >/dev/null 2>&1; then \
		echo "$(YELLOW)執行 checkov...$(NC)"; \
		checkov -d $(TERRAFORM_DIR) --framework terraform; \
	else \
		echo "$(YELLOW)checkov 未安裝，跳過$(NC)"; \
	fi

# ============================================================================
# 單元測試
# ============================================================================

test-unit: ## 執行單元測試
	@echo "$(BLUE)執行單元測試...$(NC)"
	@cd $(TESTS_DIR) && go mod tidy
	@cd $(TESTS_DIR) && go test -v -timeout $(GO_TEST_TIMEOUT) ./unit/...
	@echo "$(GREEN)單元測試完成$(NC)"

test-unit-vpc: ## 執行 VPC 模組單元測試
	@echo "$(BLUE)執行 VPC 單元測試...$(NC)"
	@cd $(TESTS_DIR) && go test -v -timeout $(GO_TEST_TIMEOUT) ./unit/vpc_test.go

test-unit-eks: ## 執行 EKS 模組單元測試
	@echo "$(BLUE)執行 EKS 單元測試...$(NC)"
	@cd $(TESTS_DIR) && go test -v -timeout $(GO_TEST_TIMEOUT) ./unit/eks_test.go

# ============================================================================
# 整合測試
# ============================================================================

test-integration: ## 執行整合測試
	@echo "$(BLUE)執行整合測試...$(NC)"
	@echo "$(YELLOW)警告：整合測試會在 AWS 中創建實際資源，可能產生費用$(NC)"
	@read -p "確定要繼續嗎？(y/N) " -n 1 -r; \
	echo; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		cd $(TESTS_DIR) && go test -v -timeout $(GO_TEST_TIMEOUT) ./integration/...; \
	else \
		echo "$(YELLOW)整合測試已取消$(NC)"; \
	fi

# ============================================================================
# 完整測試套件
# ============================================================================

test-fast: test-static test-lint ## 執行快速測試（不包含 AWS 資源）
	@echo "$(GREEN)快速測試完成$(NC)"

test-all: test-static test-lint test-security test-unit ## 執行所有測試（不包含整合測試）
	@echo "$(GREEN)所有測試完成$(NC)"

test-ci: test-all ## CI/CD 環境測試
	@echo "$(GREEN)CI/CD 測試完成$(NC)"

# ============================================================================
# 清理和設置
# ============================================================================

clean: ## 清理測試產生的文件
	@echo "$(BLUE)清理測試文件...$(NC)"
	@rm -f terraform.tfplan
	@rm -f .terraform.lock.hcl
	@rm -rf .terraform/
	@rm -rf $(TESTS_DIR)/vendor/
	@echo "$(GREEN)清理完成$(NC)"

setup: ## 設置測試環境
	@echo "$(BLUE)設置測試環境...$(NC)"
	@cp terraform.tfvars.example tests/fixtures/test.tfvars
	@echo "$(YELLOW)請編輯 tests/fixtures/test.tfvars 以符合您的測試環境$(NC)"
	@cd $(TESTS_DIR) && go mod tidy
	@echo "$(GREEN)測試環境設置完成$(NC)"

# ============================================================================
# 文檔生成
# ============================================================================

docs: ## 生成模組文檔
	@echo "$(BLUE)生成文檔...$(NC)"
	@if command -v terraform-docs >/dev/null 2>&1; then \
		terraform-docs markdown table --output-file README.md .; \
		for dir in modules/*/; do \
			terraform-docs markdown table --output-file README.md "$$dir"; \
		done; \
		echo "$(GREEN)文檔生成完成$(NC)"; \
	else \
		echo "$(YELLOW)terraform-docs 未安裝，跳過文檔生成$(NC)"; \
	fi
