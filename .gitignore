# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*
*.tfstate.backup

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.tfvars
*.tfvars.json
!example.tfvars

# Ignore override files as they are usually used for local development
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Ignore lock files
.terraform.lock.hcl

# Ignore provider binary directory
.terraform.d/

# Ignore any local backup files
*.backup
*.bak
*.old
*~

# Ignore .DS_Store files on macOS
.DS_Store
._*

# Ignore IDE and editor files
.idea/
.vscode/
*.swp
*.swo
*.swn

# Ignore local environment files
.env
.env.*
!.env.example

# Ignore Terraform plan output files
*.tfplan
*.plan

# Ignore terraform override files
*_override.tf
*_override.tf.json

# Ignore terragrunt files
.terragrunt*

# Ignore local backend configuration
backend.tf
backend.hcl

# Testing files
tests/vendor/
tests/*.log
tests/test-results/
tests/coverage/
*.test
*.out

# Go testing
go.sum
coverage.txt
coverage.html

# Test artifacts
test-*.tfvars
*-test.tfvars
test-output/
