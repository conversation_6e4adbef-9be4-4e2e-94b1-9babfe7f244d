# Terraform Backend 配置範例
# 複製此檔案為 backend.conf 並修改相應的值
# 使用方式: terraform init -backend-config=backend.conf

bucket                = "your-terraform-state-bucket"
key                   = "infra/terraform.tfstate"
region                = "ap-east-2"
encrypt               = true
skip_region_validation = true

# DynamoDB 表用於 state locking（強烈建議啟用）
dynamodb_table        = "your-cluster-name-terraform-state-lock"
