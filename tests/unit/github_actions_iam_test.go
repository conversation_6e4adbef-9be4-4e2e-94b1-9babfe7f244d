package test

import (
	"testing"

	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

func TestGitHubActionsIAMModule(t *testing.T) {
	t.<PERSON><PERSON><PERSON>()

	// 設定 Terraform 選項
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// Terraform 模組的路徑
		TerraformDir: "../../modules/github-actions-iam",

		// 變數設定
		Vars: map[string]interface{}{
			"role_name":                "test-github-actions",
			"github_org":               "test-org",
			"enable_repo_restrictions": false, // 測試無限制模式
			"cluster_name":             "test-cluster",
			"region":                   "ap-east-2",
			"ecr_repository_arns": []string{
				"arn:aws:ecr:ap-east-2:123456789012:repository/test-app",
			},
			"tags": map[string]string{
				"Environment": "test",
				"Project":     "terratest",
			},
		},

		// 禁用顏色輸出以便在 CI 中更好地顯示
		NoColor: true,
	})

	// 在測試結束時清理資源
	defer terraform.Destroy(t, terraformOptions)

	// 執行 terraform init 和 plan
	terraform.InitAndPlan(t, terraformOptions)

	// 驗證計劃沒有錯誤
	planOutput := terraform.Plan(t, terraformOptions)
	assert.Contains(t, planOutput, "aws_iam_role.github_actions")
	assert.Contains(t, planOutput, "aws_iam_policy.ecr_policy")
	assert.Contains(t, planOutput, "aws_iam_policy.eks_deploy_policy")
}

func TestGitHubActionsIAMModuleValidation(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name        string
		vars        map[string]interface{}
		expectError bool
	}{
		{
			name: "valid unrestricted configuration",
			vars: map[string]interface{}{
				"role_name":                "valid-role",
				"github_org":               "valid-org",
				"enable_repo_restrictions": false,
				"cluster_name":             "valid-cluster",
				"region":                   "ap-east-2",
			},
			expectError: false,
		},
		{
			name: "valid restricted configuration",
			vars: map[string]interface{}{
				"role_name":                "valid-role",
				"github_org":               "valid-org",
				"github_repo":              "valid-repo",
				"enable_repo_restrictions": true,
				"github_branches":          []string{"main", "develop"},
				"cluster_name":             "valid-cluster",
				"region":                   "ap-east-2",
			},
			expectError: false,
		},
		{
			name: "invalid github org name",
			vars: map[string]interface{}{
				"role_name":                "test-role",
				"github_org":               "invalid@org",
				"enable_repo_restrictions": false,
				"cluster_name":             "valid-cluster",
				"region":                   "ap-east-2",
			},
			expectError: true,
		},
		{
			name: "restricted mode without repo name",
			vars: map[string]interface{}{
				"role_name":                "test-role",
				"github_org":               "valid-org",
				"enable_repo_restrictions": true,
				"github_repo":              "", // 空的 repo 名稱在限制模式下應該失敗
				"cluster_name":             "valid-cluster",
				"region":                   "ap-east-2",
			},
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
				TerraformDir: "../../modules/github-actions-iam",
				Vars:         tc.vars,
				NoColor:      true,
			})

			if tc.expectError {
				// 預期會有錯誤
				_, err := terraform.InitAndPlanE(t, terraformOptions)
				assert.Error(t, err)
			} else {
				// 預期沒有錯誤
				terraform.InitAndPlan(t, terraformOptions)
			}
		})
	}
}

func TestGitHubActionsIAMOutputs(t *testing.T) {
	t.Parallel()

	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		TerraformDir: "../../modules/github-actions-iam",
		Vars: map[string]interface{}{
			"role_name":                "test-github-actions",
			"github_org":               "test-org",
			"enable_repo_restrictions": false,
			"cluster_name":             "test-cluster",
			"region":                   "ap-east-2",
		},
		NoColor: true,
	})

	defer terraform.Destroy(t, terraformOptions)

	// 只執行 plan 來檢查輸出
	terraform.InitAndPlan(t, terraformOptions)

	// 檢查預期的輸出是否存在
	planOutput := terraform.Plan(t, terraformOptions)
	
	// 檢查是否包含預期的輸出
	expectedOutputs := []string{
		"github_actions_role_arn",
		"github_actions_role_name",
		"github_oidc_provider_arn",
		"ecr_policy_arn",
		"eks_deploy_policy_arn",
	}

	for _, output := range expectedOutputs {
		assert.Contains(t, planOutput, output, "Expected output %s not found in plan", output)
	}
}
