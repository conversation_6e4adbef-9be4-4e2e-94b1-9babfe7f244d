package test

import (
	"testing"

	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

// TestVPCModule 測試 VPC 模組的基本功能
func TestVPCModule(t *testing.T) {
	t.Parallel()

	// 設置 Terraform 選項
	terraformOptions := &terraform.Options{
		// Terraform 代碼的路徑
		TerraformDir: "../../modules/vpc",

		// 變數設置
		Vars: map[string]interface{}{
			"vpc_cidr":           "10.0.0.0/16",
			"availability_zones": []string{"ap-east-2a", "ap-east-2b"},
			"private_subnets":    []string{"10.0.1.0/24", "10.0.2.0/24"},
			"public_subnets":     []string{"10.0.101.0/24", "10.0.102.0/24"},
			"tags": map[string]string{
				"Environment": "test",
				"Project":     "vpc-test",
			},
		},

		// 禁用顏色輸出以便在 CI 中更好地顯示
		NoColor: true,
	}

	// 在測試結束時清理資源
	defer terraform.Destroy(t, terraformOptions)

	// 初始化和應用 Terraform
	terraform.InitAndPlan(t, terraformOptions)

	// 驗證計劃不會產生錯誤
	planOutput := terraform.Plan(t, terraformOptions)
	assert.Contains(t, planOutput, "Plan:")
}

// TestVPCModuleValidation 測試 VPC 模組的變數驗證
func TestVPCModuleValidation(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name        string
		vars        map[string]interface{}
		expectError bool
	}{
		{
			name: "valid_configuration",
			vars: map[string]interface{}{
				"vpc_cidr":           "10.0.0.0/16",
				"availability_zones": []string{"ap-east-2a"},
				"private_subnets":    []string{"10.0.1.0/24"},
				"public_subnets":     []string{"10.0.101.0/24"},
			},
			expectError: false,
		},
		{
			name: "invalid_vpc_cidr",
			vars: map[string]interface{}{
				"vpc_cidr":           "invalid-cidr",
				"availability_zones": []string{"ap-east-2a"},
				"private_subnets":    []string{"10.0.1.0/24"},
				"public_subnets":     []string{"10.0.101.0/24"},
			},
			expectError: true,
		},
		{
			name: "empty_availability_zones",
			vars: map[string]interface{}{
				"vpc_cidr":           "10.0.0.0/16",
				"availability_zones": []string{},
				"private_subnets":    []string{"10.0.1.0/24"},
				"public_subnets":     []string{"10.0.101.0/24"},
			},
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			terraformOptions := &terraform.Options{
				TerraformDir: "../../modules/vpc",
				Vars:         tc.vars,
				NoColor:      true,
			}

			// 初始化 Terraform
			terraform.Init(t, terraformOptions)

			// 驗證配置
			if tc.expectError {
				// 期望驗證失敗
				_, err := terraform.ValidateE(t, terraformOptions)
				assert.Error(t, err, "Expected validation to fail for %s", tc.name)
			} else {
				// 期望驗證成功
				terraform.Validate(t, terraformOptions)
			}
		})
	}
}

// TestVPCModuleOutputs 測試 VPC 模組的輸出
func TestVPCModuleOutputs(t *testing.T) {
	t.Parallel()

	terraformOptions := &terraform.Options{
		TerraformDir: "../../modules/vpc",
		Vars: map[string]interface{}{
			"vpc_cidr":           "10.0.0.0/16",
			"availability_zones": []string{"ap-east-2a", "ap-east-2b"},
			"private_subnets":    []string{"10.0.1.0/24", "10.0.2.0/24"},
			"public_subnets":     []string{"10.0.101.0/24", "10.0.102.0/24"},
		},
		NoColor: true,
	}

	// 初始化和計劃
	terraform.InitAndPlan(t, terraformOptions)

	// 檢查計劃中是否包含預期的資源
	planOutput := terraform.Plan(t, terraformOptions)
	
	// 驗證 VPC 資源會被創建
	assert.Contains(t, planOutput, "aws_vpc.this")
	
	// 驗證子網路資源會被創建
	assert.Contains(t, planOutput, "aws_subnet.private")
	assert.Contains(t, planOutput, "aws_subnet.public")
	
	// 驗證網路閘道資源會被創建
	assert.Contains(t, planOutput, "aws_internet_gateway.this")
	assert.Contains(t, planOutput, "aws_nat_gateway.this")
}
