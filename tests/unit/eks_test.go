package test

import (
	"testing"

	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

// TestEKSModule 測試 EKS 模組的基本功能
func TestEKSModule(t *testing.T) {
	t.<PERSON>()

	terraformOptions := &terraform.Options{
		TerraformDir: "../../modules/eks",
		Vars: map[string]interface{}{
			"cluster_name":       "test-cluster",
			"cluster_version":    "1.33",
			"vpc_id":            "vpc-12345678",
			"private_subnet_ids": []string{"subnet-12345678", "subnet-87654321"},
			"allowed_cidr_blocks": []string{"10.0.0.0/16"},
			"node_groups": map[string]interface{}{
				"test_group": map[string]interface{}{
					"desired_capacity": 1,
					"max_capacity":     2,
					"min_capacity":     1,
					"instance_types":   []string{"t4g.small"},
				},
			},
			"tags": map[string]string{
				"Environment": "test",
				"Project":     "eks-test",
			},
		},
		NoColor: true,
	}

	// 在測試結束時清理資源
	defer terraform.Destroy(t, terraformOptions)

	// 初始化和計劃
	terraform.InitAndPlan(t, terraformOptions)

	// 驗證計劃不會產生錯誤
	planOutput := terraform.Plan(t, terraformOptions)
	assert.Contains(t, planOutput, "Plan:")
}

// TestEKSModuleValidation 測試 EKS 模組的變數驗證
func TestEKSModuleValidation(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name        string
		vars        map[string]interface{}
		expectError bool
	}{
		{
			name: "valid_cluster_name",
			vars: map[string]interface{}{
				"cluster_name":       "valid-cluster-name",
				"cluster_version":    "1.33",
				"vpc_id":            "vpc-12345678",
				"private_subnet_ids": []string{"subnet-12345678"},
			},
			expectError: false,
		},
		{
			name: "invalid_cluster_name_uppercase",
			vars: map[string]interface{}{
				"cluster_name":       "Invalid-Cluster-Name",
				"cluster_version":    "1.33",
				"vpc_id":            "vpc-12345678",
				"private_subnet_ids": []string{"subnet-12345678"},
			},
			expectError: true,
		},
		{
			name: "invalid_cluster_name_too_long",
			vars: map[string]interface{}{
				"cluster_name":       "this-cluster-name-is-way-too-long-and-exceeds-the-maximum-allowed-length-for-eks-cluster-names",
				"cluster_version":    "1.33",
				"vpc_id":            "vpc-12345678",
				"private_subnet_ids": []string{"subnet-12345678"},
			},
			expectError: true,
		},
		{
			name: "invalid_kubernetes_version",
			vars: map[string]interface{}{
				"cluster_name":       "test-cluster",
				"cluster_version":    "1.99",
				"vpc_id":            "vpc-12345678",
				"private_subnet_ids": []string{"subnet-12345678"},
			},
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			terraformOptions := &terraform.Options{
				TerraformDir: "../../modules/eks",
				Vars:         tc.vars,
				NoColor:      true,
			}

			terraform.Init(t, terraformOptions)

			if tc.expectError {
				_, err := terraform.ValidateE(t, terraformOptions)
				assert.Error(t, err, "Expected validation to fail for %s", tc.name)
			} else {
				terraform.Validate(t, terraformOptions)
			}
		})
	}
}

// TestEKSNodeGroupConfiguration 測試 EKS 節點群組配置
func TestEKSNodeGroupConfiguration(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name       string
		nodeGroups map[string]interface{}
		expectPlan bool
	}{
		{
			name: "single_node_group",
			nodeGroups: map[string]interface{}{
				"default": map[string]interface{}{
					"desired_capacity": 2,
					"max_capacity":     3,
					"min_capacity":     1,
					"instance_types":   []string{"t4g.medium"},
				},
			},
			expectPlan: true,
		},
		{
			name: "multiple_node_groups",
			nodeGroups: map[string]interface{}{
				"general": map[string]interface{}{
					"desired_capacity": 2,
					"max_capacity":     3,
					"min_capacity":     1,
					"instance_types":   []string{"t4g.medium"},
				},
				"compute": map[string]interface{}{
					"desired_capacity": 1,
					"max_capacity":     2,
					"min_capacity":     0,
					"instance_types":   []string{"c5.large"},
				},
			},
			expectPlan: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			terraformOptions := &terraform.Options{
				TerraformDir: "../../modules/eks",
				Vars: map[string]interface{}{
					"cluster_name":       "test-cluster",
					"cluster_version":    "1.33",
					"vpc_id":            "vpc-12345678",
					"private_subnet_ids": []string{"subnet-12345678", "subnet-87654321"},
					"node_groups":        tc.nodeGroups,
				},
				NoColor: true,
			}

			terraform.InitAndPlan(t, terraformOptions)

			if tc.expectPlan {
				planOutput := terraform.Plan(t, terraformOptions)
				assert.Contains(t, planOutput, "Plan:")
				
				// 驗證節點群組資源會被創建
				for groupName := range tc.nodeGroups {
					assert.Contains(t, planOutput, groupName)
				}
			}
		})
	}
}
