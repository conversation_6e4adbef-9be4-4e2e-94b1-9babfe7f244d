# Terraform 測試框架

## 概覽

本測試框架為 infra-taipei 專案提供完整的測試解決方案，包括靜態分析、單元測試、整合測試和安全掃描。

## 目錄結構

```
tests/
├── unit/                    # 單元測試
│   ├── vpc_test.go         # VPC 模組測試
│   ├── eks_test.go         # EKS 模組測試
│   └── ...                 # 其他模組測試
├── integration/             # 整合測試
│   ├── full_deployment_test.go
│   └── connectivity_test.go
├── fixtures/                # 測試配置文件
│   ├── test.tfvars         # 標準測試配置
│   └── minimal.tfvars      # 最小測試配置
├── scripts/                 # 測試腳本
│   ├── setup.sh            # 環境設置腳本
│   └── cleanup.sh          # 清理腳本
├── go.mod                   # Go 模組定義
└── README.md               # 本文檔
```

## 快速開始

### 1. 環境設置

```bash
# 執行設置腳本
./tests/scripts/setup.sh

# 或手動設置
cd tests
go mod tidy
```

### 2. 執行測試

```bash
# 執行所有快速測試
make test-fast

# 執行靜態分析
make test-static

# 執行單元測試
make test-unit

# 執行安全掃描
make test-security

# 執行所有測試（不包含整合測試）
make test-all
```

### 3. 整合測試（謹慎使用）

```bash
# 整合測試會在 AWS 中創建實際資源
make test-integration
```

## 測試類型

### 靜態分析

- **Terraform 格式檢查**: `terraform fmt -check`
- **語法驗證**: `terraform validate`
- **Linting**: `tflint`
- **最佳實踐檢查**: 自定義規則

### 單元測試

使用 [Terratest](https://terratest.gruntwork.io/) 框架：

- **模組驗證**: 測試各個 Terraform 模組
- **變數驗證**: 測試輸入變數的驗證規則
- **輸出測試**: 驗證模組輸出
- **計劃測試**: 驗證 Terraform 計劃

### 整合測試

- **完整部署**: 測試整個基礎設施的部署
- **服務連通性**: 測試服務間的連接
- **功能測試**: 測試特定功能是否正常工作
- **標籤驗證**: 驗證資源標籤

### 安全掃描

- **tfsec**: Terraform 安全掃描
- **Checkov**: 基礎設施即代碼安全掃描
- **自定義安全規則**: 專案特定的安全檢查

## 配置文件

### test.tfvars

標準測試配置，包含：
- 最小資源配置以節省成本
- 測試專用的命名
- 適合自動化測試的設置

### minimal.tfvars

最小配置，用於：
- 快速驗證
- 基本功能測試
- 開發環境測試

## 測試最佳實踐

### 1. 測試分層

```
快速測試 (< 5 分鐘)
├── 靜態分析
├── Linting
└── 單元測試

中等測試 (5-15 分鐘)
├── 安全掃描
└── 部分整合測試

慢速測試 (15+ 分鐘)
└── 完整整合測試
```

### 2. 成本控制

- 使用最小實例類型
- 限制資源數量
- 自動清理測試資源
- 使用 LocalStack（本地測試）

### 3. 並行執行

```bash
# 並行執行單元測試
go test -parallel 4 ./unit/...

# 使用 make 並行執行
make -j4 test-static test-unit
```

## CI/CD 整合

### GitHub Actions

工作流程包含：
- 靜態分析
- 單元測試
- 安全掃描
- 條件性整合測試

### 觸發條件

- **Push**: 執行快速測試
- **Pull Request**: 執行完整測試套件
- **手動觸發**: 可選擇執行整合測試

## 故障排除

### 常見問題

#### 1. AWS 憑證問題
```bash
# 檢查 AWS 憑證
aws sts get-caller-identity

# 配置 AWS 憑證
aws configure
```

#### 2. Go 模組問題
```bash
# 清理並重新下載
go clean -modcache
go mod tidy
go mod download
```

#### 3. Terraform 初始化問題
```bash
# 清理並重新初始化
rm -rf .terraform
terraform init -backend=false
```

#### 4. 測試超時
```bash
# 增加測試超時時間
go test -timeout 45m ./integration/...
```

### 調試技巧

```bash
# 詳細輸出
go test -v ./unit/vpc_test.go

# 執行特定測試
go test -run TestVPCModule ./unit/

# 保留測試資源（調試用）
export SKIP_TEARDOWN=true
go test ./integration/...
```

## 開發指南

### 添加新測試

1. **單元測試**:
   ```go
   func TestNewModule(t *testing.T) {
       // 測試邏輯
   }
   ```

2. **整合測試**:
   ```go
   func TestNewIntegration(t *testing.T) {
       if testing.Short() {
           t.Skip("跳過整合測試")
       }
       // 測試邏輯
   }
   ```

### 測試命名規範

- 測試文件: `*_test.go`
- 測試函數: `Test*`
- 基準測試: `Benchmark*`
- 範例測試: `Example*`

## 效能考量

### 測試優化

- 使用 `t.Parallel()` 並行執行
- 快取 Go 模組和 Terraform 提供者
- 重用測試資源
- 使用最小配置

### 資源管理

- 自動清理測試資源
- 監控 AWS 成本
- 設置資源限制
- 使用測試專用帳戶

## 安全考量

- 不在代碼中硬編碼憑證
- 使用 IAM 角色而非用戶
- 限制測試環境權限
- 定期輪換測試憑證

## 貢獻指南

1. 添加測試時請更新文檔
2. 確保新測試通過 CI/CD
3. 遵循現有的測試模式
4. 添加適當的錯誤處理

## 相關資源

- [Terratest 文檔](https://terratest.gruntwork.io/)
- [Terraform 測試最佳實踐](https://www.terraform.io/docs/extend/testing/index.html)
- [Go 測試指南](https://golang.org/doc/tutorial/add-a-test)
- [AWS 測試策略](https://aws.amazon.com/builders-library/automating-safe-hands-off-deployments/)
