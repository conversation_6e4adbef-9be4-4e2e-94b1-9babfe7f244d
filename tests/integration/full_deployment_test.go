package test

import (
	"fmt"
	"testing"
	"time"

	"github.com/gruntwork-io/terratest/modules/aws"
	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

// TestFullDeployment 測試完整的基礎設施部署
// 警告：此測試會在 AWS 中創建實際資源，可能產生費用
func TestFullDeployment(t *testing.T) {
	// 設置測試超時時間（EKS 部署通常需要 15-20 分鐘）
	if testing.Short() {
		t.Skip("跳過整合測試（使用 -short 標誌）")
	}

	// 生成唯一的測試 ID
	testID := fmt.Sprintf("test-%d", time.Now().Unix())
	
	terraformOptions := &terraform.Options{
		TerraformDir: "../../",
		VarFiles:     []string{"tests/fixtures/test.tfvars"},
		Vars: map[string]interface{}{
			"cluster_name": fmt.Sprintf("test-cluster-%s", testID),
		},
		NoColor: true,
	}

	// 在測試結束時清理資源
	defer terraform.Destroy(t, terraformOptions)

	// 初始化和應用 Terraform
	terraform.InitAndApply(t, terraformOptions)

	// 驗證 VPC 已創建
	vpcID := terraform.Output(t, terraformOptions, "vpc_id")
	assert.NotEmpty(t, vpcID)

	// 驗證 VPC 存在於 AWS 中
	awsRegion := "ap-east-2"
	vpc := aws.GetVpcById(t, vpcID, awsRegion)
	assert.Equal(t, vpcID, vpc.Id)

	// 驗證 EKS 叢集已創建
	clusterName := terraform.Output(t, terraformOptions, "cluster_name")
	assert.NotEmpty(t, clusterName)

	// 驗證 EKS 叢集狀態
	cluster := aws.GetEksCluster(t, awsRegion, clusterName)
	assert.Equal(t, "ACTIVE", cluster.Status)

	// 驗證 RDS 實例已創建
	rdsEndpoint := terraform.Output(t, terraformOptions, "rds_endpoint")
	assert.NotEmpty(t, rdsEndpoint)

	// 驗證 ElastiCache 叢集已創建
	cacheEndpoint := terraform.Output(t, terraformOptions, "elasticache_endpoint")
	assert.NotEmpty(t, cacheEndpoint)
}

// TestMinimalDeployment 測試最小配置部署
func TestMinimalDeployment(t *testing.T) {
	if testing.Short() {
		t.Skip("跳過整合測試（使用 -short 標誌）")
	}

	testID := fmt.Sprintf("minimal-%d", time.Now().Unix())
	
	terraformOptions := &terraform.Options{
		TerraformDir: "../../",
		VarFiles:     []string{"tests/fixtures/minimal.tfvars"},
		Vars: map[string]interface{}{
			"cluster_name": fmt.Sprintf("minimal-test-%s", testID),
		},
		NoColor: true,
	}

	defer terraform.Destroy(t, terraformOptions)

	// 初始化和應用
	terraform.InitAndApply(t, terraformOptions)

	// 基本驗證
	vpcID := terraform.Output(t, terraformOptions, "vpc_id")
	assert.NotEmpty(t, vpcID)

	clusterName := terraform.Output(t, terraformOptions, "cluster_name")
	assert.NotEmpty(t, clusterName)
}

// TestDeploymentWithVault 測試包含 Vault 的部署
func TestDeploymentWithVault(t *testing.T) {
	if testing.Short() {
		t.Skip("跳過整合測試（使用 -short 標誌）")
	}

	testID := fmt.Sprintf("vault-%d", time.Now().Unix())
	
	terraformOptions := &terraform.Options{
		TerraformDir: "../../",
		VarFiles:     []string{"tests/fixtures/test.tfvars"},
		Vars: map[string]interface{}{
			"cluster_name": fmt.Sprintf("vault-test-%s", testID),
			"enable_vault": true,
		},
		NoColor: true,
	}

	defer terraform.Destroy(t, terraformOptions)

	terraform.InitAndApply(t, terraformOptions)

	// 驗證 Vault 相關資源
	vaultKMSKeyID := terraform.Output(t, terraformOptions, "vault_kms_key_id")
	assert.NotEmpty(t, vaultKMSKeyID)

	// 驗證 KMS 金鑰存在
	awsRegion := "ap-east-2"
	kmsKey := aws.GetKmsKey(t, awsRegion, vaultKMSKeyID)
	assert.Equal(t, "Enabled", kmsKey.KeyState)
}

// TestResourceTagging 測試資源標籤
func TestResourceTagging(t *testing.T) {
	if testing.Short() {
		t.Skip("跳過整合測試（使用 -short 標誌）")
	}

	testID := fmt.Sprintf("tags-%d", time.Now().Unix())
	expectedTags := map[string]string{
		"Environment": "test",
		"Project":     "taipei-infra-test",
		"Owner":       "test-team",
		"ManagedBy":   "terraform",
	}
	
	terraformOptions := &terraform.Options{
		TerraformDir: "../../",
		VarFiles:     []string{"tests/fixtures/test.tfvars"},
		Vars: map[string]interface{}{
			"cluster_name": fmt.Sprintf("tags-test-%s", testID),
			"tags":         expectedTags,
		},
		NoColor: true,
	}

	defer terraform.Destroy(t, terraformOptions)

	terraform.InitAndApply(t, terraformOptions)

	// 驗證 VPC 標籤
	vpcID := terraform.Output(t, terraformOptions, "vpc_id")
	awsRegion := "ap-east-2"
	vpc := aws.GetVpcById(t, vpcID, awsRegion)
	
	for key, expectedValue := range expectedTags {
		actualValue, exists := vpc.Tags[key]
		assert.True(t, exists, "標籤 %s 不存在", key)
		assert.Equal(t, expectedValue, actualValue, "標籤 %s 的值不正確", key)
	}
}
