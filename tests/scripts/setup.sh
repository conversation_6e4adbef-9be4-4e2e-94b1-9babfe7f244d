#!/bin/bash

# ============================================================================
# 測試環境設置腳本
# ============================================================================

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函數定義
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查必要工具
check_tools() {
    log_info "檢查必要工具..."
    
    local tools=("terraform" "go" "aws")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_info "請安裝缺少的工具後重新運行此腳本"
        exit 1
    fi
    
    log_success "所有必要工具已安裝"
}

# 檢查 AWS 憑證
check_aws_credentials() {
    log_info "檢查 AWS 憑證..."
    
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS 憑證未配置或無效"
        log_info "請運行 'aws configure' 設置 AWS 憑證"
        exit 1
    fi
    
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    local region=$(aws configure get region)
    
    log_success "AWS 憑證有效 (Account: $account_id, Region: $region)"
}

# 設置 Go 模組
setup_go_module() {
    log_info "設置 Go 模組..."
    
    cd "$(dirname "$0")/.."
    
    if [ ! -f "go.mod" ]; then
        log_error "go.mod 文件不存在"
        exit 1
    fi
    
    go mod tidy
    go mod download
    
    log_success "Go 模組設置完成"
}

# 創建測試配置
setup_test_config() {
    log_info "設置測試配置..."
    
    local test_config="fixtures/test.tfvars"
    local example_config="../terraform.tfvars.example"
    
    if [ ! -f "$test_config" ]; then
        if [ -f "$example_config" ]; then
            cp "$example_config" "$test_config"
            log_warning "已從範例創建測試配置文件: $test_config"
            log_warning "請編輯此文件以符合您的測試環境"
        else
            log_error "找不到範例配置文件: $example_config"
            exit 1
        fi
    else
        log_success "測試配置文件已存在: $test_config"
    fi
}

# 驗證 Terraform 配置
validate_terraform() {
    log_info "驗證 Terraform 配置..."
    
    cd "$(dirname "$0")/../.."
    
    terraform init -backend=false
    terraform validate
    
    log_success "Terraform 配置驗證通過"
}

# 創建測試 S3 儲存桶（如果需要）
setup_test_bucket() {
    log_info "檢查測試 S3 儲存桶..."
    
    local bucket_name="test-terraform-state-$(date +%s)"
    local region=$(aws configure get region || echo "ap-east-2")
    
    # 檢查是否已有測試儲存桶配置
    if grep -q "test-terraform-state-bucket" tests/fixtures/test.tfvars; then
        log_info "更新測試配置中的 S3 儲存桶名稱..."
        sed -i.bak "s/test-terraform-state-bucket/$bucket_name/g" tests/fixtures/test.tfvars
        rm -f tests/fixtures/test.tfvars.bak
    fi
    
    log_success "測試 S3 儲存桶配置完成: $bucket_name"
}

# 主函數
main() {
    log_info "開始設置測試環境..."
    
    check_tools
    check_aws_credentials
    setup_go_module
    setup_test_config
    validate_terraform
    setup_test_bucket
    
    log_success "測試環境設置完成！"
    log_info ""
    log_info "接下來您可以運行："
    log_info "  make test-static    # 執行靜態分析"
    log_info "  make test-unit      # 執行單元測試"
    log_info "  make test-all       # 執行所有測試"
    log_info ""
    log_warning "注意：整合測試會在 AWS 中創建實際資源，可能產生費用"
}

# 執行主函數
main "$@"
