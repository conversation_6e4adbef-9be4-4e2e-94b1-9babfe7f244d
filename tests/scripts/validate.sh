#!/bin/bash

# ============================================================================
# 測試框架驗證腳本
# ============================================================================

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函數定義
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 驗證測試框架結構
validate_structure() {
    log_info "驗證測試框架結構..."
    
    local required_dirs=(
        "tests/unit"
        "tests/integration"
        "tests/fixtures"
        "tests/scripts"
    )
    
    local required_files=(
        "tests/go.mod"
        "tests/README.md"
        "tests/fixtures/test.tfvars"
        "tests/fixtures/minimal.tfvars"
        "Makefile"
        ".tflint.hcl"
        ".github/workflows/terraform-test.yml"
    )
    
    local missing_items=()
    
    # 檢查目錄
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            missing_items+=("目錄: $dir")
        fi
    done
    
    # 檢查文件
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_items+=("文件: $file")
        fi
    done
    
    if [ ${#missing_items[@]} -ne 0 ]; then
        log_error "缺少必要的測試框架組件："
        for item in "${missing_items[@]}"; do
            echo "  - $item"
        done
        return 1
    fi
    
    log_success "測試框架結構驗證通過"
}

# 驗證 Go 模組
validate_go_module() {
    log_info "驗證 Go 模組..."

    local original_dir=$(pwd)
    cd "$(dirname "$0")/.."
    
    if command -v go >/dev/null 2>&1; then
        if ! go mod verify; then
            log_error "Go 模組驗證失敗"
            cd "$original_dir"
            return 1
        fi

        if ! go mod tidy -diff; then
            log_warning "Go 模組需要整理"
            go mod tidy
        fi

        log_success "Go 模組驗證通過"
    else
        log_warning "Go 未安裝，跳過 Go 模組驗證"
    fi

    cd "$original_dir"
}

# 驗證 Terraform 配置
validate_terraform_config() {
    log_info "驗證 Terraform 配置..."

    local original_dir=$(pwd)
    local script_dir="$(dirname "$0")"
    local project_root="$(cd "$script_dir/../.." && pwd)"
    cd "$project_root"
    
    if command -v terraform >/dev/null 2>&1; then
        # 檢查格式
        if ! terraform fmt -check -recursive .; then
            log_error "Terraform 格式檢查失敗"
            log_info "執行 'terraform fmt -recursive .' 修復格式問題"
            cd "$original_dir"
            return 1
        fi

        # 初始化（不使用 backend）
        terraform init -backend=false > /dev/null

        # 驗證語法
        if ! terraform validate; then
            log_error "Terraform 語法驗證失敗"
            cd "$original_dir"
            return 1
        fi

        log_success "Terraform 配置驗證通過"
    else
        log_warning "Terraform 未安裝，跳過 Terraform 配置驗證"
    fi

    cd "$original_dir"
}

# 驗證測試配置文件
validate_test_configs() {
    log_info "驗證測試配置文件..."

    local original_dir=$(pwd)
    local script_dir="$(dirname "$0")"
    local project_root="$(cd "$script_dir/../.." && pwd)"

    local config_files=(
        "$project_root/tests/fixtures/test.tfvars"
        "$project_root/tests/fixtures/minimal.tfvars"
    )
    
    for config in "${config_files[@]}"; do
        if [ ! -f "$config" ]; then
            log_error "測試配置文件不存在: $config"
            return 1
        fi
        
        # 檢查配置文件語法（如果 terraform 可用）
        if command -v terraform >/dev/null 2>&1; then
            cd "$project_root"
            if ! terraform validate -var-file="$config" > /dev/null 2>&1; then
                log_warning "測試配置文件可能有問題: $config"
            fi
            cd "$original_dir"
        fi
    done
    
    log_success "測試配置文件驗證通過"
}

# 驗證 Makefile 目標
validate_makefile() {
    log_info "驗證 Makefile 目標..."

    local original_dir=$(pwd)
    local script_dir="$(dirname "$0")"
    local project_root="$(cd "$script_dir/../.." && pwd)"
    cd "$project_root"

    local required_targets=(
        "help"
        "test-static"
        "test-unit"
        "test-all"
        "fmt"
        "validate"
        "clean"
    )

    if [ -f "Makefile" ]; then
        for target in "${required_targets[@]}"; do
            if ! make -n "$target" > /dev/null 2>&1; then
                log_error "Makefile 目標不存在或有錯誤: $target"
                cd "$original_dir"
                return 1
            fi
        done
        log_success "Makefile 驗證通過"
    else
        log_warning "Makefile 不存在，跳過驗證"
    fi

    cd "$original_dir"
}

# 驗證腳本權限
validate_script_permissions() {
    log_info "驗證腳本權限..."

    local script_dir="$(dirname "$0")"
    local project_root="$(cd "$script_dir/../.." && pwd)"

    local scripts=(
        "$project_root/tests/scripts/setup.sh"
        "$project_root/tests/scripts/cleanup.sh"
        "$project_root/tests/scripts/validate.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ ! -x "$script" ]; then
            log_warning "腳本沒有執行權限: $script"
            chmod +x "$script"
            log_info "已設置執行權限: $script"
        fi
    done
    
    log_success "腳本權限驗證通過"
}

# 執行快速測試
run_quick_tests() {
    log_info "執行快速測試..."

    local original_dir=$(pwd)
    local script_dir="$(dirname "$0")"
    local project_root="$(cd "$script_dir/../.." && pwd)"

    # 測試 Go 編譯（如果 Go 可用）
    if command -v go >/dev/null 2>&1; then
        cd "$script_dir/.."
        if ! go build ./... > /dev/null 2>&1; then
            log_warning "Go 代碼編譯失敗（可能是正常的，因為這是測試代碼）"
        fi
    fi

    # 測試 Terraform 計劃（使用測試配置）
    if command -v terraform >/dev/null 2>&1; then
        cd "$project_root"
        if [ -f "tests/fixtures/minimal.tfvars" ]; then
            if ! terraform plan -var-file=tests/fixtures/minimal.tfvars -out=/dev/null > /dev/null 2>&1; then
                log_warning "Terraform 計劃生成失敗（可能需要 AWS 憑證）"
            fi
        fi
    fi

    cd "$original_dir"
    log_success "快速測試通過"
}

# 顯示驗證摘要
show_validation_summary() {
    log_info ""
    log_info "驗證摘要："
    log_info "=========="
    log_success "✓ 測試框架結構完整"
    log_success "✓ Go 模組配置正確"
    log_success "✓ Terraform 配置有效"
    log_success "✓ 測試配置文件正確"
    log_success "✓ Makefile 目標可用"
    log_success "✓ 腳本權限正確"
    log_success "✓ 快速測試通過"
    
    log_info ""
    log_info "測試框架已準備就緒！"
    log_info ""
    log_info "接下來您可以："
    log_info "  make help           # 查看所有可用命令"
    log_info "  make test-static    # 執行靜態分析"
    log_info "  make test-unit      # 執行單元測試"
    log_info "  make test-all       # 執行所有測試"
    log_info ""
}

# 主函數
main() {
    log_info "開始驗證測試框架..."
    
    local validation_steps=(
        "validate_structure"
        "validate_go_module"
        "validate_terraform_config"
        "validate_test_configs"
        "validate_makefile"
        "validate_script_permissions"
        "run_quick_tests"
    )
    
    local failed_steps=()
    
    for step in "${validation_steps[@]}"; do
        if ! $step; then
            failed_steps+=("$step")
        fi
    done
    
    if [ ${#failed_steps[@]} -ne 0 ]; then
        log_error "驗證失敗的步驟："
        for step in "${failed_steps[@]}"; do
            echo "  - $step"
        done
        exit 1
    fi
    
    show_validation_summary
    log_success "測試框架驗證完成！"
}

# 執行主函數
main "$@"
