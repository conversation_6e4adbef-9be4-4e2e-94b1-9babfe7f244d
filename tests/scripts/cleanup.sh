#!/bin/bash

# ============================================================================
# 測試資源清理腳本
# ============================================================================

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函數定義
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清理 Terraform 文件
cleanup_terraform_files() {
    log_info "清理 Terraform 文件..."
    
    local files_to_remove=(
        "terraform.tfplan"
        ".terraform.lock.hcl"
        "terraform.tfstate"
        "terraform.tfstate.backup"
        "*.tfplan"
        "*.plan"
    )
    
    for pattern in "${files_to_remove[@]}"; do
        find . -name "$pattern" -type f -delete 2>/dev/null || true
    done
    
    # 清理 .terraform 目錄
    find . -name ".terraform" -type d -exec rm -rf {} + 2>/dev/null || true
    
    log_success "Terraform 文件清理完成"
}

# 清理 Go 測試文件
cleanup_go_files() {
    log_info "清理 Go 測試文件..."
    
    cd "$(dirname "$0")/.."
    
    # 清理 Go 模組快取
    go clean -modcache 2>/dev/null || true
    
    # 清理測試快取
    go clean -testcache 2>/dev/null || true
    
    # 清理 vendor 目錄
    rm -rf vendor/ 2>/dev/null || true
    
    log_success "Go 測試文件清理完成"
}

# 清理測試產生的 AWS 資源
cleanup_aws_resources() {
    log_info "檢查並清理測試 AWS 資源..."
    
    local region=$(aws configure get region || echo "ap-east-2")
    
    # 清理測試 EKS 叢集
    log_info "檢查測試 EKS 叢集..."
    local test_clusters=$(aws eks list-clusters --region "$region" --query 'clusters[?contains(@, `test-`) || contains(@, `minimal-`)]' --output text 2>/dev/null || true)
    
    if [ -n "$test_clusters" ]; then
        log_warning "發現測試 EKS 叢集: $test_clusters"
        log_warning "請手動清理這些叢集以避免持續費用"
        
        for cluster in $test_clusters; do
            log_info "叢集: $cluster"
            log_info "清理命令: aws eks delete-cluster --name $cluster --region $region"
        done
    fi
    
    # 清理測試 VPC
    log_info "檢查測試 VPC..."
    local test_vpcs=$(aws ec2 describe-vpcs --region "$region" --filters "Name=tag:Project,Values=*test*" --query 'Vpcs[].VpcId' --output text 2>/dev/null || true)
    
    if [ -n "$test_vpcs" ]; then
        log_warning "發現測試 VPC: $test_vpcs"
        log_warning "請確認這些 VPC 可以安全刪除"
    fi
    
    # 清理測試 S3 儲存桶
    log_info "檢查測試 S3 儲存桶..."
    local test_buckets=$(aws s3api list-buckets --query 'Buckets[?contains(Name, `test-terraform-state`)].Name' --output text 2>/dev/null || true)
    
    if [ -n "$test_buckets" ]; then
        log_warning "發現測試 S3 儲存桶: $test_buckets"
        
        for bucket in $test_buckets; do
            log_info "儲存桶: $bucket"
            
            # 檢查儲存桶是否為空
            local object_count=$(aws s3api list-objects-v2 --bucket "$bucket" --query 'KeyCount' --output text 2>/dev/null || echo "0")
            
            if [ "$object_count" -eq 0 ]; then
                log_info "儲存桶為空，可以安全刪除"
                log_info "刪除命令: aws s3 rb s3://$bucket"
            else
                log_warning "儲存桶包含 $object_count 個物件"
                log_info "清空命令: aws s3 rm s3://$bucket --recursive"
                log_info "刪除命令: aws s3 rb s3://$bucket"
            fi
        done
    fi
    
    log_success "AWS 資源檢查完成"
}

# 清理測試日誌
cleanup_logs() {
    log_info "清理測試日誌..."
    
    local log_files=(
        "*.log"
        "crash.log"
        "crash.*.log"
        "test-*.log"
    )
    
    for pattern in "${log_files[@]}"; do
        find . -name "$pattern" -type f -delete 2>/dev/null || true
    done
    
    log_success "測試日誌清理完成"
}

# 重置測試配置
reset_test_config() {
    log_info "重置測試配置..."
    
    local test_config="tests/fixtures/test.tfvars"
    local example_config="terraform.tfvars.example"
    
    if [ -f "$example_config" ] && [ -f "$test_config" ]; then
        # 備份當前配置
        cp "$test_config" "$test_config.backup.$(date +%s)"
        
        # 重置為範例配置
        cp "$example_config" "$test_config"
        
        log_success "測試配置已重置（已備份舊配置）"
    else
        log_warning "無法重置測試配置（缺少必要文件）"
    fi
}

# 顯示清理摘要
show_cleanup_summary() {
    log_info ""
    log_info "清理摘要："
    log_info "=========="
    log_success "✓ Terraform 文件已清理"
    log_success "✓ Go 測試文件已清理"
    log_success "✓ 測試日誌已清理"
    log_success "✓ AWS 資源已檢查"
    
    log_info ""
    log_warning "請注意："
    log_warning "- 如果有 AWS 資源仍在運行，請手動清理以避免費用"
    log_warning "- 測試配置已重置，請在下次測試前重新配置"
    log_info ""
}

# 主函數
main() {
    log_info "開始清理測試環境..."
    
    # 確認清理操作
    if [ "$1" != "--force" ]; then
        log_warning "此操作將清理所有測試文件和檢查 AWS 資源"
        read -p "確定要繼續嗎？(y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "清理操作已取消"
            exit 0
        fi
    fi
    
    cleanup_terraform_files
    cleanup_go_files
    cleanup_logs
    cleanup_aws_resources
    
    if [ "$1" = "--reset-config" ] || [ "$2" = "--reset-config" ]; then
        reset_test_config
    fi
    
    show_cleanup_summary
    
    log_success "測試環境清理完成！"
}

# 顯示幫助
show_help() {
    echo "用法: $0 [選項]"
    echo ""
    echo "選項:"
    echo "  --force         強制清理，不詢問確認"
    echo "  --reset-config  重置測試配置文件"
    echo "  --help          顯示此幫助訊息"
    echo ""
    echo "範例:"
    echo "  $0                    # 互動式清理"
    echo "  $0 --force           # 強制清理"
    echo "  $0 --force --reset-config  # 強制清理並重置配置"
}

# 處理命令行參數
case "$1" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
