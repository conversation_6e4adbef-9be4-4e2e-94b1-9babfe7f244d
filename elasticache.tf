# 安裝 EKS 附加元件
module "elasticache" {
  source = "./modules/elasticache"

  # 使用標準化命名
  identifier           = local.service_names.elasticache
  node_type            = var.elasticache_node_type
  num_cache_nodes      = var.elasticache_num_nodes
  engine_version       = var.elasticache_engine_version
  parameter_group_name = var.elasticache_parameter_group

  # 將 ElastiCache 放置在私有子網路中以提高安全性
  vpc_id              = module.vpc.vpc_id
  subnet_ids          = module.vpc.private_subnets
  security_group_ids  = [module.eks.cluster_security_group_id]
  allowed_cidr_blocks = var.allowed_cidr_blocks

  tags = local.resource_specific_tags.cache_tags
}
