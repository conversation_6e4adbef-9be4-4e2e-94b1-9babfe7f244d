# VPC 模組，使用官方 terraform-aws-modules/vpc/aws 模組
module "vpc" {
  source = "./modules/vpc"

  # 使用標準化命名
  resource_prefix      = local.resource_prefix
  region               = var.region
  vpc_cidr             = var.vpc_cidr
  availability_zones   = var.availability_zones
  private_subnets      = var.private_subnets
  public_subnets       = var.public_subnets
  enable_vpc_endpoints = true
  tags                 = local.resource_specific_tags.vpc_tags
}
