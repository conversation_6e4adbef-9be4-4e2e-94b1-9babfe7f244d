# ============================================================================
# TFLint 配置文件
# ============================================================================
# TFLint 是 Terraform 的 linter，用於檢查最佳實踐和潛在錯誤

config {
  # 啟用模組檢查
  module = true
  
  # 強制執行，即使有警告也要失敗
  force = false
  
  # 禁用顏色輸出（適用於 CI/CD）
  disabled_by_default = false
}

# AWS Provider 規則集
plugin "aws" {
  enabled = true
  version = "0.27.0"
  source  = "github.com/terraform-linters/tflint-ruleset-aws"
}

# 啟用的規則
rule "terraform_deprecated_interpolation" {
  enabled = true
}

rule "terraform_deprecated_index" {
  enabled = true
}

rule "terraform_unused_declarations" {
  enabled = true
}

rule "terraform_comment_syntax" {
  enabled = true
}

rule "terraform_documented_outputs" {
  enabled = true
}

rule "terraform_documented_variables" {
  enabled = true
}

rule "terraform_typed_variables" {
  enabled = true
}

rule "terraform_module_pinned_source" {
  enabled = true
}

rule "terraform_naming_convention" {
  enabled = true
  format  = "snake_case"
}

rule "terraform_standard_module_structure" {
  enabled = true
}

# AWS 特定規則
rule "aws_instance_invalid_type" {
  enabled = true
}

rule "aws_instance_previous_type" {
  enabled = true
}

rule "aws_alb_invalid_security_group" {
  enabled = true
}

rule "aws_alb_invalid_subnet" {
  enabled = true
}

rule "aws_elasticache_cluster_invalid_type" {
  enabled = true
}

rule "aws_db_instance_invalid_type" {
  enabled = true
}

rule "aws_route_invalid_route_table" {
  enabled = true
}

rule "aws_route_invalid_gateway" {
  enabled = true
}

# 禁用的規則（根據專案需求調整）
rule "aws_instance_invalid_ami" {
  enabled = false  # 因為我們使用動態 AMI 查詢
}

rule "terraform_required_version" {
  enabled = false  # 我們在 terraform.tf 中已經定義
}

rule "terraform_required_providers" {
  enabled = false  # 我們在 terraform.tf 中已經定義
}
