# Terraform 變數配置範例
# 複製此檔案為 terraform.tfvars 並修改相應的值

# 基本配置
region = "ap-east-2"

# 網路配置
vpc_cidr = "**********/16"
availability_zones = ["ap-east-2a", "ap-east-2b", "ap-east-2c"]
private_subnets = ["172.11.1.0/24", "172.11.2.0/24", "172.11.3.0/24"]
public_subnets = ["172.11.101.0/24", "172.11.102.0/24", "172.11.103.0/24"]

# 允許的 CIDR 區塊（用於安全組規則）
allowed_cidr_blocks = ["**********/16"]

# 額外的 IAM 政策（用於 EKS 節點群組）
additional_iam_policies = [
  "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy",
  "arn:aws:iam::aws:policy/AmazonKMSFullAccess"
]

# EKS 配置
cluster_name = "longshun-tpe"
cluster_version = "1.33"

# Route53 和 SSL 憑證配置
route53_zone_id = "Z04339392B2NE9OVTLRBO"
domain_name = "longshun.io"
subject_alternative_names = ["*.longshun.io"]

# Backend 配置（用於參考）
terraform_state_bucket = "longshun-taipei-terraform"
terraform_state_key = "infra/terraform.tfstate"
terraform_state_lock_table = "longshun-tpe-terraform-state-lock"

# 標準化標籤 - 這些標籤會自動合併到 locals.common_tags 中
tags = {
  Environment = "dev"
  Project     = "taipei-infra"
  Owner       = "infrastructure-team"
  CostCenter  = "engineering"
  # 注意：Terraform 和 ManagedBy 標籤會自動添加
}

# RDS 配置 - identifier 會自動使用標準化命名
db_identifier = "longshun-tpe-pg"  # 這個值會被 local.service_names.rds 覆蓋
db_instance_class = "db.t4g.medium"
db_allocated_storage = 20
db_name = "wb"
db_username = "postgres"

# ElastiCache 配置 - identifier 會自動使用標準化命名
elasticache_identifier = "longshun-tpe-cache"  # 這個值會被 local.service_names.elasticache 覆蓋
elasticache_node_type = "cache.t4g.medium"
elasticache_num_nodes = 1
elasticache_engine_version = "7.2"
elasticache_parameter_group = "default.valkey7"

# EKS 節點群組配置
node_groups = {
  default_node_group = {
    desired_capacity = 2
    max_capacity     = 3
    min_capacity     = 1
    instance_types   = ["t4g.medium"]
  }
}

# EKS 附加元件配置
enable_aws_load_balancer_controller = true
aws_load_balancer_controller_chart_version = "1.6.2"
enable_cluster_autoscaler = true
cluster_autoscaler_chart_version = "9.29.0"
enable_external_dns = true
external_dns_chart_version = "1.13.1"
enable_external_secrets = true
external_secrets_chart_version = "0.9.11"
enable_metrics_server = true
enable_reloader = true
reloader_chart_version = "1.0.69"
enable_vault = true
vault_chart_version = "0.25.0"

# External DNS 配置
external_dns_domain_filters = ["longshun.io", "dev.longshun.io"]
external_dns_txt_prefix = "cname-"
external_dns_policy = "upsert-only"
external_dns_hosted_zone_arns = ["arn:aws:route53:::hostedzone/*"]

# GitHub Actions IAM 配置
enable_github_actions_iam = true
github_org = "your-github-org"

# Repository 和分支限制（預設為 false，允許組織內所有 repositories）
github_enable_repo_restrictions = false
# 當 github_enable_repo_restrictions = true 時，需要設定以下變數：
# github_repo = "your-app-repo"
# github_allowed_branches = ["main", "develop", "staging"]

# GitHub Actions ECR 存取權限（可選，留空則允許存取所有 ECR repositories）
github_actions_ecr_repository_arns = [
  # "arn:aws:ecr:ap-east-2:123456789012:repository/your-app",
  # "arn:aws:ecr:ap-east-2:123456789012:repository/your-api"
]

# GitHub Actions 額外權限（可選）
github_actions_additional_assume_role_arns = [
  # "arn:aws:iam::123456789012:role/additional-deployment-role"
]

github_actions_additional_policy_arns = [
  # "arn:aws:iam::aws:policy/ReadOnlyAccess"
]

# GitHub OIDC Provider 設定
github_actions_create_oidc_provider = true
# github_actions_existing_oidc_provider_arn = "arn:aws:iam::123456789012:oidc-provider/token.actions.githubusercontent.com"
