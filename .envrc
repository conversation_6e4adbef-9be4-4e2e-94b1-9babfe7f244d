# ============================================================================
# Terraform Infrastructure Environment Configuration
# 此檔案供 direnv 和 devbox 使用，設定 Terraform 專案所需的環境變數
# ============================================================================

# ----------------------------------------------------------------------------
# AWS 認證設定
# ----------------------------------------------------------------------------
# AWS 存取金鑰 (建議使用 AWS CLI 或 IAM roles 而非硬編碼)
# export AWS_ACCESS_KEY_ID="your-access-key-id"
# export AWS_SECRET_ACCESS_KEY="your-secret-access-key"

# AWS 區域設定 (與 terraform.tf 中的 backend 設定一致)
export AWS_REGION="ap-east-2"
export AWS_DEFAULT_REGION="ap-east-2"

# AWS Profile (如果使用 AWS CLI profiles)
export AWS_PROFILE="longshun-taipei"

# ----------------------------------------------------------------------------
# Terraform 設定
# ----------------------------------------------------------------------------
# Terraform 後端配置 (S3 bucket 和 DynamoDB table)
export TF_VAR_terraform_state_bucket="longshun-taipei-terraform"
export TF_VAR_terraform_state_key="infra/terraform.tfstate"
export TF_VAR_terraform_state_lock_table="longshun-tpe-terraform-state-lock"

# Terraform 工作區設定 (dev/staging/prod)
# export TF_WORKSPACE="dev"

# Terraform 日誌等級 (TRACE, DEBUG, INFO, WARN, ERROR)
export TF_LOG="INFO"

# Terraform 插件快取目錄 (提升性能)
export TF_PLUGIN_CACHE_DIR="$HOME/.terraform.d/plugin-cache"

# ----------------------------------------------------------------------------
# 專案特定變數
# ----------------------------------------------------------------------------
# 基本配置
export TF_VAR_region="ap-east-2"
export TF_VAR_environment="dev"
export TF_VAR_project_name="taipei-infra"
export TF_VAR_application_name="infrastructure"
export TF_VAR_owner="infrastructure-team"

# EKS 叢集設定
export TF_VAR_cluster_name="longshun-tpe"
export TF_VAR_cluster_version="1.33"

# 網路設定
export TF_VAR_vpc_cidr="**********/16"
export TF_VAR_availability_zones='["ap-east-2a", "ap-east-2b", "ap-east-2c"]'
export TF_VAR_private_subnets='["**********/24", "**********/24", "**********/24"]'
export TF_VAR_public_subnets='["************/24", "************/24", "************/24"]'

# Route53 和 SSL 憑證設定
export TF_VAR_route53_zone_id="Z04339392B2NE9OVTLRBO"
export TF_VAR_domain_name="longshun.io"
export TF_VAR_subject_alternative_names='["*.longshun.io"]'

# RDS 設定
export TF_VAR_db_identifier="longshun-tpe-pg"
export TF_VAR_db_instance_class="db.t4g.medium"
export TF_VAR_db_allocated_storage="20"
export TF_VAR_db_name="wb"
export TF_VAR_db_username="postgres"

# ElastiCache 設定
export TF_VAR_elasticache_identifier="longshun-tpe-cache"
export TF_VAR_elasticache_node_type="cache.t4g.medium"
export TF_VAR_elasticache_num_nodes="1"
export TF_VAR_elasticache_engine_version="7.2"

# GitHub Actions IAM 設定
export TF_VAR_enable_github_actions_iam="true"
export TF_VAR_github_org="your-github-org"
export TF_VAR_github_actions_create_oidc_provider="true"

# ----------------------------------------------------------------------------
# 開發工具設定
# ----------------------------------------------------------------------------
# Go 設定 (用於測試)
export GO_TEST_TIMEOUT="30m"
export GOPATH="$HOME/go"

# Kubernetes 設定
export KUBECONFIG="$HOME/.kube/config"

# Docker 設定 (如果需要)
# export DOCKER_BUILDKIT=1

# ----------------------------------------------------------------------------
# 安全性設定
# ----------------------------------------------------------------------------
# 敏感資料警告
echo "⚠️  請確保不要在此檔案中硬編碼敏感資訊（如 AWS 金鑰、密碼等）"
echo "💡 建議使用 AWS CLI profiles 或 IAM roles 進行認證"

# 檢查必要工具
if ! command -v terraform &> /dev/null; then
    echo "❌ Terraform 未安裝，請先安裝 Terraform"
fi

if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI 未安裝，請先安裝 AWS CLI"
fi

# 建立 Terraform 插件快取目錄
mkdir -p "$TF_PLUGIN_CACHE_DIR"

# ----------------------------------------------------------------------------
# 專案路徑設定
# ----------------------------------------------------------------------------
# 將專案的 scripts 目錄加入 PATH (如果存在)
if [ -d "./scripts" ]; then
    export PATH="./scripts:$PATH"
fi

# 將測試工具加入 PATH
if [ -d "./tests/scripts" ]; then
    export PATH="./tests/scripts:$PATH"
fi

# ----------------------------------------------------------------------------
# 開發環境提示
# ----------------------------------------------------------------------------
echo "🚀 Terraform 開發環境已載入"
echo "📁 專案: $(basename "$PWD")"
echo "🌍 AWS 區域: $AWS_REGION"
echo "🏗️  環境: ${TF_VAR_environment:-dev}"
echo ""
echo "常用命令:"
echo "  terraform init -backend-config=backend.conf"
echo "  terraform plan -var-file=terraform.tfvars"
echo "  terraform apply -var-file=terraform.tfvars"
echo "  make test-static    # 執行靜態分析"
echo "  make test-unit      # 執行單元測試"
echo ""

# ----------------------------------------------------------------------------
# 環境變數驗證
# ----------------------------------------------------------------------------
# 檢查 AWS 認證是否設定
if [ -z "$AWS_ACCESS_KEY_ID" ] && [ -z "$AWS_PROFILE" ]; then
    echo "⚠️  AWS 認證未設定，請設定 AWS_PROFILE 或 AWS_ACCESS_KEY_ID"
fi

# 檢查 Terraform 變數檔案是否存在
if [ ! -f "terraform.tfvars" ]; then
    echo "⚠️  terraform.tfvars 不存在，請複製 terraform.tfvars.example 並修改"
fi

# 檢查後端配置檔案是否存在
if [ ! -f "backend.conf" ]; then
    echo "⚠️  backend.conf 不存在，請複製 backend.conf.example 並修改"
fi
