output "vpc_id" {
  description = "VPC ID"
  value       = module.vpc.vpc_id
}

output "private_subnet_ids" {
  description = "私有子網 ID 列表"
  value       = module.vpc.private_subnets
}

output "public_subnet_ids" {
  description = "公有子網 ID 列表"
  value       = module.vpc.public_subnets
}

output "eks_cluster_id" {
  description = "EKS 叢集 ID"
  value       = module.eks.cluster_id
}

output "eks_cluster_endpoint" {
  description = "EKS 叢集端點"
  value       = module.eks.cluster_endpoint
}

output "eks_cluster_security_group_id" {
  description = "EKS 叢集安全群組 ID"
  value       = module.eks.cluster_security_group_id
}

# RDS 相關輸出
output "db_instance_id" {
  description = "RDS 執行個體 ID"
  value       = try(module.rds.db_instance_id, null)
}

output "db_instance_endpoint" {
  description = "RDS 執行個體端點"
  value       = try(module.rds.db_instance_endpoint, null)
}

output "db_instance_name" {
  description = "RDS 資料庫名稱"
  value       = try(module.rds.db_instance_name, null)
}

output "db_security_group_id" {
  description = "RDS 安全群組 ID"
  value       = try(module.rds.db_security_group_id, null)
}

# 密碼相關輸出
output "db_password_secret_id" {
  description = "資料庫密碼的 Secrets Manager ID"
  value       = try(module.secrets.secret_id, null)
}

output "db_password_secret_arn" {
  description = "資料庫密碼的 Secrets Manager ARN"
  value       = try(module.secrets.secret_arn, null)
}

output "db_password" {
  description = "資料庫密碼"
  value       = try(random_password.db_password.result, null)
  sensitive   = true
}

# Vault 相關輸出
# 使用 locals.tf 中集中管理的 Vault 配置

# 輸出 Vault 是否啟用
output "vault_enabled" {
  description = "Vault 是否已啟用"
  value       = local.vault_enabled
}

# 輸出 Vault root token 密鑰名稱
output "vault_root_token_secret_name" {
  description = "包含 Vault root token 的 AWS Secrets Manager 秘密名稱"
  value = try(
    module.eks_addons.vault_root_token_secret_name,
    local.vault_root_token_secret_name,
    null
  )
  sensitive = true
}

# 輸出 Vault recovery keys 密鑰名稱
output "vault_recovery_keys_secret_name" {
  description = "包含 Vault recovery keys 的 AWS Secrets Manager 秘密名稱"
  value = try(
    module.eks_addons.vault_recovery_keys_secret_name,
    local.vault_recovery_keys_secret_name,
    null
  )
  sensitive = true
}

# 輸出 Vault 命名空間
output "vault_namespace" {
  description = "Vault 所在的 Kubernetes 命名空間"
  value = try(
    module.eks_addons.vault_namespace,
    local.vault_namespace,
    null
  )
}

# 輸出 Vault 服務名稱
output "vault_service_name" {
  description = "Vault 服務名稱"
  value = try(
    module.eks_addons.vault_service_name,
    local.vault_service_name,
    null
  )
}

# ============================================================================
# 標籤策略輸出
# ============================================================================

# 輸出標籤合規性摘要
output "tag_compliance_summary" {
  description = "標籤合規性摘要"
  value       = local.tag_compliance_summary
}

# 輸出缺失的必要標籤
output "missing_required_tags" {
  description = "缺失的必要標籤列表"
  value       = local.missing_required_tags
}

# 輸出標籤驗證錯誤
output "tag_validation_errors" {
  description = "標籤驗證錯誤列表"
  value       = local.tag_validation_errors
}

# 輸出所有應用的標籤
output "applied_tags" {
  description = "應用到資源的完整標籤集合"
  value       = local.common_tags
  sensitive   = false
}

# 輸出資源特定標籤
output "resource_specific_tags" {
  description = "各類資源的特定標籤"
  value = {
    eks_tags   = local.resource_specific_tags.eks_tags
    vpc_tags   = local.resource_specific_tags.vpc_tags
    rds_tags   = local.resource_specific_tags.rds_tags
    cache_tags = local.resource_specific_tags.cache_tags
    kms_tags   = local.resource_specific_tags.kms_tags
  }
  sensitive = false
}

# ElastiCache 相關輸出
output "elasticache_cluster_id" {
  description = "ElastiCache 叢集 ID"
  value       = try(module.elasticache.cluster_id, null)
}

output "elasticache_endpoint" {
  description = "ElastiCache 端點"
  value       = try(module.elasticache.cache_nodes[0].address, null)
}

output "elasticache_port" {
  description = "ElastiCache 連接埠"
  value       = try(module.elasticache.port, null)
}

# KMS 相關輸出
output "kms_key_id" {
  description = "KMS 金鑰 ID"
  value       = try(module.kms.key_id, null)
}

output "kms_key_arn" {
  description = "KMS 金鑰 ARN"
  value       = try(module.kms.key_arn, null)
}

# ACM 相關輸出
output "acm_certificate_arn" {
  description = "ACM 憑證 ARN"
  value       = try(module.acm_cert.certificate_arn, null)
}

output "acm_certificate_domain_validation_options" {
  description = "ACM 憑證網域驗證選項"
  value       = try(module.acm_cert.domain_validation_options, {})
  sensitive   = true
}

# EKS Addons 相關輸出
output "eks_addons_status" {
  description = "EKS 附加元件狀態摘要"
  value = {
    aws_load_balancer_controller = try(var.enable_aws_load_balancer_controller, false)
    cluster_autoscaler           = try(var.enable_cluster_autoscaler, false)
    external_dns                 = try(var.enable_external_dns, false)
    external_secrets             = try(var.enable_external_secrets, false)
    metrics_server               = try(var.enable_metrics_server, false)
    reloader                     = try(var.enable_reloader, false)
    vault                        = local.vault_enabled
  }
}

# 注意：我們不再嘗試直接輸出 vault_root_token 的值
# 這樣可以避免 Terraform 在應用過程中嘗試讀取可能不存在的密鑰

# GitHub Actions IAM 相關輸出
output "github_actions_role_arn" {
  description = "GitHub Actions IAM role ARN"
  value       = var.enable_github_actions_iam ? module.github_actions_iam[0].github_actions_role_arn : null
}

output "github_actions_role_name" {
  description = "GitHub Actions IAM role 名稱"
  value       = var.enable_github_actions_iam ? module.github_actions_iam[0].github_actions_role_name : null
}

output "github_oidc_provider_arn" {
  description = "GitHub OIDC provider ARN"
  value       = var.enable_github_actions_iam ? module.github_actions_iam[0].github_oidc_provider_arn : null
}

output "github_actions_workflow_example" {
  description = "GitHub Actions workflow 配置範例"
  value       = var.enable_github_actions_iam ? module.github_actions_iam[0].github_workflow_example : null
}
