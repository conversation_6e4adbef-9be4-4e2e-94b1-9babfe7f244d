terraform {
  required_version = ">= 1.5.0"

  backend "s3" {
    # 注意：backend 配置不支援變數，這些值需要在 terraform init 時指定
    # 或使用 -backend-config 參數
    bucket                 = "longshun-taipei-terraform"
    key                    = "infra/terraform.tfstate"
    region                 = "ap-east-2"
    encrypt                = true
    skip_region_validation = true

    # DynamoDB 表用於 state locking
    dynamodb_table = "longshun-tpe-terraform-state-lock"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.20"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.5"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.5"
    }
    vault = {
      source  = "hashicorp/vault"
      version = "~> 3.20"
    }
    kubectl = {
      source  = "gavinbu<PERSON>/kubectl"
      version = "~> 1.14"
    }
  }
}
