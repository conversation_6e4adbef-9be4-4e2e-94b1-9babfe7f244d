# GitHub Actions 快速開始指南

## 🚀 立即可用的配置

您的 GitHub Actions IAM role 已經建立完成，可以立即使用！

### 📋 基本資訊

- **IAM Role ARN**: `arn:aws:iam::920372997208:role/longshun-tpe-github-actions`
- **OIDC Provider**: `arn:aws:iam::920372997208:oidc-provider/token.actions.githubusercontent.com`
- **允許的組織**: `longshun-eco`
- **允許的 repositories**: 組織內所有 repositories
- **允許的分支**: 所有分支

## 🔧 GitHub Repository 設定

### 1. 設定 Repository Secrets

在您的 GitHub repository 中，前往 **Settings > Secrets and variables > Actions**，加入：

```
Name: AWS_ROLE_ARN
Value: arn:aws:iam::920372997208:role/longshun-tpe-github-actions
```

### 2. 基本 Workflow 範例

建立 `.github/workflows/deploy.yml`：

```yaml
name: Deploy to EKS

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

# 必要的權限設定
permissions:
  id-token: write   # 用於 OIDC 認證
  contents: read    # 讀取 repository 內容

env:
  AWS_REGION: ap-east-2
  EKS_CLUSTER_NAME: longshun-tpe

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          role-session-name: GitHubActions-${{ github.run_id }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Test AWS access
        run: |
          aws sts get-caller-identity
          aws eks describe-cluster --name ${{ env.EKS_CLUSTER_NAME }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: List ECR repositories
        run: |
          aws ecr describe-repositories --region ${{ env.AWS_REGION }}
```

## 🐳 Docker 建置和推送範例

```yaml
      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: your-app-name
          IMAGE_TAG: ${{ github.sha }}
        run: |
          # 建置 Docker image
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:latest .
          
          # 推送到 ECR
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
```

## ☸️ Kubernetes 部署範例

```yaml
      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region ${{ env.AWS_REGION }} --name ${{ env.EKS_CLUSTER_NAME }}

      - name: Deploy to Kubernetes
        env:
          IMAGE_URI: ${{ steps.login-ecr.outputs.registry }}/your-app-name:${{ github.sha }}
        run: |
          # 更新 deployment
          kubectl set image deployment/your-app your-app=$IMAGE_URI -n default
          
          # 等待 rollout 完成
          kubectl rollout status deployment/your-app -n default --timeout=300s
          
          # 檢查部署狀態
          kubectl get pods -n default -l app=your-app
```

## 🔍 測試 Workflow

### 簡單測試 Workflow

建立 `.github/workflows/test-aws-access.yml`：

```yaml
name: Test AWS Access

on:
  workflow_dispatch:  # 手動觸發

permissions:
  id-token: write
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::920372997208:role/longshun-tpe-github-actions
          role-session-name: TestAccess
          aws-region: ap-east-2

      - name: Test AWS CLI
        run: |
          echo "Testing AWS access..."
          aws sts get-caller-identity
          
          echo "Testing ECR access..."
          aws ecr describe-repositories --region ap-east-2 || echo "No ECR repositories found"
          
          echo "Testing EKS access..."
          aws eks describe-cluster --name longshun-tpe --region ap-east-2
          
          echo "All tests passed!"
```

## 📦 建立 ECR Repository

在使用前，您需要建立 ECR repositories：

```bash
# 建立 ECR repository
aws ecr create-repository --repository-name your-app-name --region ap-east-2

# 設定 lifecycle policy（可選）
aws ecr put-lifecycle-policy \
  --repository-name your-app-name \
  --lifecycle-policy-text '{
    "rules": [
      {
        "rulePriority": 1,
        "selection": {
          "tagStatus": "untagged",
          "countType": "sinceImagePushed",
          "countUnit": "days",
          "countNumber": 7
        },
        "action": {
          "type": "expire"
        }
      }
    ]
  }' \
  --region ap-east-2
```

## 🔐 安全最佳實踐

### 1. 分支保護
```yaml
# 只在特定分支部署
on:
  push:
    branches: [main]  # 只在 main 分支部署到生產環境
```

### 2. 環境分離
```yaml
jobs:
  deploy-staging:
    if: github.ref == 'refs/heads/develop'
    # 部署到 staging 環境
    
  deploy-production:
    if: github.ref == 'refs/heads/main'
    # 部署到 production 環境
```

### 3. 安全掃描
```yaml
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.IMAGE_TAG }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
```

## 🚨 故障排除

### 常見錯誤

1. **"Error: could not assume role"**
   ```
   確認：
   - Repository 在 longshun-eco 組織下
   - Workflow 有正確的 permissions 設定
   - AWS_ROLE_ARN secret 設定正確
   ```

2. **"Error: AccessDenied"**
   ```
   確認：
   - ECR repository 存在
   - EKS 叢集名稱正確 (longshun-tpe)
   - Region 設定正確 (ap-east-2)
   ```

### 除錯步驟

```yaml
      - name: Debug AWS access
        run: |
          echo "Current AWS identity:"
          aws sts get-caller-identity
          
          echo "Available ECR repositories:"
          aws ecr describe-repositories --region ap-east-2 || true
          
          echo "EKS cluster info:"
          aws eks describe-cluster --name longshun-tpe --region ap-east-2 || true
```

## 📞 支援

如果遇到問題，請檢查：

1. **IAM Role**: `arn:aws:iam::920372997208:role/longshun-tpe-github-actions`
2. **OIDC Provider**: `arn:aws:iam::920372997208:oidc-provider/token.actions.githubusercontent.com`
3. **Trust Relationship**: 允許 `repo:longshun-eco/*`

---

**準備就緒！** 🎉 您現在可以開始使用 GitHub Actions 進行 CI/CD 部署了！
