# Terraform 變數驗證規則

## 概覽
本文件記錄了為 Terraform 專案添加的所有變數驗證規則，這些規則有助於在部署前捕獲配置錯誤。

## 已添加的驗證規則

### 1. 基礎設施配置

#### AWS 區域 (`region`)
- **格式驗證**: 必須符合 AWS 區域命名規範
- **範例**: `us-east-1`, `ap-east-2`, `eu-west-1`
- **錯誤訊息**: "區域必須是有效的 AWS 區域格式"

#### VPC CIDR (`vpc_cidr`)
- **格式驗證**: 必須是有效的 CIDR 格式
- **範圍驗證**: 子網路遮罩必須在 /8 到 /28 之間
- **範例**: `10.0.0.0/16`, `172.16.0.0/12`

#### 可用區域 (`availability_zones`)
- **數量驗證**: 必須包含 2-6 個可用區域
- **格式驗證**: 每個 AZ 必須符合 AWS 格式
- **範例**: `["us-east-1a", "us-east-1b"]`

#### 子網路配置 (`private_subnets`, `public_subnets`)
- **格式驗證**: 所有 CIDR 必須是有效格式
- **數量驗證**: 必須包含 2-6 個子網
- **範例**: `["10.0.1.0/24", "10.0.2.0/24"]`

### 2. EKS 配置

#### 叢集名稱 (`cluster_name`)
- **格式驗證**: 必須以字母開頭，以字母或數字結尾
- **字元限制**: 只能包含字母、數字和連字號
- **長度限制**: 1-100 個字元
- **範例**: `my-cluster`, `prod-eks-01`

#### Kubernetes 版本 (`cluster_version`)
- **版本驗證**: 必須是 1.28 或更高版本
- **格式驗證**: 必須是 1.xx 格式
- **範例**: `1.28`, `1.29`, `1.30`

#### 節點群組配置 (`node_groups`)
- **容量驗證**: 0 ≤ min ≤ desired ≤ max ≤ 1000
- **實例類型驗證**: 必須是有效的 EC2 實例類型格式
- **範例**: `t3.medium`, `m5.large`

### 3. RDS 配置

#### 實例類型 (`db_instance_class`)
- **格式驗證**: 必須是有效的 RDS 實例類型
- **範例**: `db.t3.micro`, `db.r5.large`

#### 儲存空間 (`db_allocated_storage`)
- **範圍驗證**: 20GB 到 65536GB
- **範例**: `20`, `100`, `1000`

#### 資料庫名稱 (`db_name`)
- **格式驗證**: 必須以字母開頭，只能包含字母、數字和底線
- **長度限制**: 1-63 個字元
- **範例**: `mydb`, `app_database`

#### 使用者名稱 (`db_username`)
- **格式驗證**: 必須以字母開頭，只能包含字母、數字和底線
- **長度限制**: 1-63 個字元
- **範例**: `admin`, `db_user`

### 4. 網路安全配置

#### 允許的 CIDR 區塊 (`allowed_cidr_blocks`)
- **格式驗證**: 所有項目必須是有效的 CIDR 格式
- **數量限制**: 1-10 個項目
- **範例**: `["10.0.0.0/8", "172.16.0.0/12"]`

### 5. Route53 和 SSL 配置

#### Route53 區域 ID (`route53_zone_id`)
- **格式驗證**: 必須以 Z 開頭，後跟 10-32 個大寫字母或數字
- **範例**: `Z1234567890ABC`

#### 網域名稱 (`domain_name`)
- **格式驗證**: 必須是有效的網域格式
- **範例**: `example.com`, `my-domain.org`

### 6. IAM 配置

#### IAM 政策 ARN (`additional_iam_policies`)
- **格式驗證**: 必須是有效的 AWS IAM 政策 ARN 格式
- **範例**: `arn:aws:iam::aws:policy/PolicyName`

### 7. EKS 附加元件配置

#### External DNS 政策 (`external_dns_policy`)
- **值驗證**: 必須是 `sync` 或 `upsert-only`

#### Route53 區域類型 (`external_dns_zone_type`)
- **值驗證**: 必須是 `public` 或 `private`

### 8. Vault 配置

#### 副本數量 (`vault_replicas`)
- **範圍驗證**: 1-10 個副本
- **奇數驗證**: 必須是奇數以確保高可用性

### 9. ElastiCache 配置

#### 節點類型 (`elasticache_node_type`)
- **格式驗證**: 必須是有效的 ElastiCache 節點類型
- **範例**: `cache.t3.micro`, `cache.r5.large`

#### 節點數量 (`elasticache_num_nodes`)
- **範圍驗證**: 1-20 個節點

### 10. 模組級別驗證

#### EKS 模組
- **VPC ID**: 必須是有效的 VPC ID 格式 (`vpc-xxxxxxxx`)
- **子網 ID**: 必須是有效的子網 ID 格式 (`subnet-xxxxxxxx`)
- **最少子網**: 至少需要 2 個私有子網

#### RDS 模組
- **識別碼**: 必須以小寫字母開頭，1-63 個字元

## 使用方式

這些驗證規則會在以下情況自動執行：
1. `terraform plan` - 規劃階段
2. `terraform apply` - 應用階段
3. `terraform validate` - 驗證階段

## 測試驗證規則

要測試驗證規則是否正常工作，可以：
1. 修改 `test-validation.tf` 檔案中的註解
2. 設定無效值
3. 運行 `terraform validate` 查看錯誤訊息

## 好處

1. **早期錯誤檢測**: 在部署前捕獲配置錯誤
2. **提高安全性**: 防止不安全的配置
3. **改善用戶體驗**: 提供清晰的錯誤訊息
4. **減少調試時間**: 避免因配置錯誤導致的部署失敗
5. **強制最佳實踐**: 確保遵循 AWS 和 Kubernetes 的命名規範
