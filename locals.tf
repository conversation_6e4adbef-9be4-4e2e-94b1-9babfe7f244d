# 全域 locals 用於集中管理所有計算值、錯誤處理、條件邏輯和命名標準化
locals {
  # ============================================================================
  # 基礎配置 - 所有資源的統一命名和標籤基礎
  # ============================================================================

  # 標準化命名前綴 - 所有資源的統一命名基礎
  resource_prefix = var.cluster_name

  # ============================================================================
  # 增強標籤策略 - 企業級標籤管理
  # ============================================================================

  # 核心標籤 (必要)
  core_tags = {
    Environment = try(var.tags["Environment"], var.environment, "dev")
    Project     = try(var.tags["Project"], var.project_name, "taipei-infra")
    Application = try(var.tags["Application"], var.application_name, "infrastructure")
    Component   = try(var.tags["Component"], "platform")
    Owner       = try(var.tags["Owner"], var.owner, "infrastructure-team")
    ManagedBy   = "terraform"
    Terraform   = "true"
  }

  # 成本管理標籤
  cost_tags = {
    CostCenter   = try(var.tags["CostCenter"], var.cost_center, "engineering")
    BusinessUnit = try(var.tags["BusinessUnit"], var.business_unit, "technology")
    BillingCode  = try(var.tags["BillingCode"], var.billing_code, "infra-001")
    Budget       = try(var.tags["Budget"], var.budget, "infrastructure")
  }

  # 運維標籤
  operational_tags = {
    CreatedBy         = try(var.tags["CreatedBy"], var.created_by, "terraform")
    CreatedDate       = try(var.tags["CreatedDate"], formatdate("YYYY-MM-DD", timestamp()))
    Version           = try(var.tags["Version"], var.infrastructure_version, "1.0.0")
    MaintenanceWindow = try(var.tags["MaintenanceWindow"], var.maintenance_window, "sun:03:00-05:00")
    BackupRequired    = try(var.tags["BackupRequired"], var.backup_required, "true")
    MonitoringLevel   = try(var.tags["MonitoringLevel"], var.monitoring_level, "standard")
  }

  # 安全和合規標籤
  security_tags = {
    DataClassification = try(var.tags["DataClassification"], var.data_classification, "internal")
    SecurityLevel      = try(var.tags["SecurityLevel"], var.security_level, "medium")
    Compliance         = try(var.tags["Compliance"], var.compliance_requirements, "none")
  }

  # 自動化標籤
  automation_tags = {
    AutoStart       = try(var.tags["AutoStart"], var.auto_start, "false")
    AutoStop        = try(var.tags["AutoStop"], var.auto_stop, "false")
    LifecyclePolicy = try(var.tags["LifecyclePolicy"], var.lifecycle_policy, "standard")
  }

  # 標準化標籤 - 合併所有類別的標籤
  standard_tags = merge(
    local.core_tags,
    local.cost_tags,
    local.operational_tags,
    local.security_tags,
    local.automation_tags
  )

  # 通用標籤，合併用戶提供的標籤和標準標籤
  common_tags = merge(local.standard_tags, var.tags)

  # 資源特定標籤
  resource_specific_tags = {
    # EKS 相關標籤
    eks_tags = merge(local.common_tags, {
      "kubernetes.io/cluster/${local.resource_prefix}"     = "owned"
      "k8s.io/cluster-autoscaler/enabled"                  = "true"
      "k8s.io/cluster-autoscaler/${local.resource_prefix}" = "owned"
      ResourceType                                         = "kubernetes-cluster"
      Service                                              = "eks"
    })

    # VPC 相關標籤
    vpc_tags = merge(local.common_tags, {
      ResourceType = "network"
      Service      = "vpc"
      NetworkTier  = "core"
    })

    # RDS 相關標籤
    rds_tags = merge(local.common_tags, {
      ResourceType    = "database"
      Service         = "rds"
      Engine          = "postgresql"
      BackupRetention = "7"
    })

    # ElastiCache 相關標籤
    cache_tags = merge(local.common_tags, {
      ResourceType = "cache"
      Service      = "elasticache"
      Engine       = "valkey"
    })

    # KMS 相關標籤
    kms_tags = merge(local.common_tags, {
      ResourceType = "security"
      Service      = "kms"
      KeyUsage     = "vault-unseal"
    })
  }

  # ============================================================================
  # AWS 配置 - AWS 相關的計算值
  # ============================================================================

  # AWS 區域配置
  aws_region = var.region

  # 可用區域映射
  availability_zones_map = {
    for idx, az in var.availability_zones : idx => az
  }

  # ============================================================================
  # Kubernetes 配置 - Kubernetes 相關的計算值
  # ============================================================================

  # EKS 叢集相關配置
  eks_cluster_name = local.resource_prefix

  # Kubernetes 命名空間配置
  kubernetes_namespaces = {
    kube_system      = "kube-system"
    vault            = var.vault_namespace
    external_dns     = "external-dns"
    external_secrets = "external-secrets"
    monitoring       = "monitoring"
  }

  # 服務帳號命名
  service_accounts = {
    aws_load_balancer_controller = "aws-load-balancer-controller"
    cluster_autoscaler           = "cluster-autoscaler"
    external_dns                 = "external-dns"
    external_secrets             = "external-secrets"
    vault                        = "vault"
  }

  # EKS 附加元件配置 (從 eks-addons.tf 移動過來)
  addons_eks_cluster_name = local.resource_prefix
  token_command           = "aws eks get-token --cluster-name ${local.addons_eks_cluster_name} --region ${var.region}"

  # ============================================================================
  # 版本管理 - 集中管理所有外部模組和 Helm Chart 版本
  # ============================================================================

  # 外部 Terraform 模組版本
  module_versions = {
    eks        = "~> 20.0"
    vpc        = "~> 5.0"
    rds_aurora = "~> 9.0"
  }

  # Helm Chart 版本管理
  helm_chart_versions = {
    aws_load_balancer_controller = "1.6.2"
    cluster_autoscaler           = "9.29.0"
    external_dns                 = "1.13.1"
    external_secrets             = "0.9.11"
    metrics_server               = "3.11.0"
    reloader                     = "1.0.69"
    vault                        = "0.27.0"
  }

  # EKS Addon 版本管理
  eks_addon_versions = {
    vpc_cni            = "v1.18.1-eksbuild.1"
    coredns            = "v1.11.1-eksbuild.4"
    kube_proxy         = "v1.29.3-eksbuild.2"
    aws_ebs_csi_driver = "v1.30.0-eksbuild.1"
    metrics_server     = "v0.7.1-eksbuild.4"
  }

  # ============================================================================
  # 網路配置 - VPC 和子網路相關的計算值
  # ============================================================================

  # 網路配置驗證
  vpc_cidr_valid = can(cidrhost(var.vpc_cidr, 0))

  # 子網路配置驗證
  private_subnets_valid = alltrue([
    for cidr in var.private_subnets : can(cidrhost(cidr, 0))
  ])

  public_subnets_valid = alltrue([
    for cidr in var.public_subnets : can(cidrhost(cidr, 0))
  ])

  # 網路配置衍生值
  vpc_cidr_block = var.vpc_cidr

  # 子網路映射
  private_subnets_map = {
    for idx, cidr in var.private_subnets : "private-${idx + 1}" => {
      cidr = cidr
      az   = var.availability_zones[idx % length(var.availability_zones)]
    }
  }

  public_subnets_map = {
    for idx, cidr in var.public_subnets : "public-${idx + 1}" => {
      cidr = cidr
      az   = var.availability_zones[idx % length(var.availability_zones)]
    }
  }

  # 安全配置
  allowed_cidr_blocks_valid = alltrue([
    for cidr in var.allowed_cidr_blocks : can(cidrhost(cidr, 0))
  ])

  # ============================================================================
  # Vault 配置 - 集中管理所有 Vault 相關的計算值 (從 outputs.tf 移動過來)
  # ============================================================================

  # Vault 啟用狀態檢查，使用多層 try 確保安全
  vault_enabled = try(var.enable_vault, false)

  # Vault 可以啟用的條件檢查
  vault_can_be_enabled = local.vault_enabled

  # Vault 相關命名
  vault_root_token_secret_name    = local.vault_enabled ? "${var.cluster_name}-vault-root-token" : ""
  vault_recovery_keys_secret_name = local.vault_enabled ? "${var.cluster_name}-vault-recovery-keys" : ""
  vault_namespace                 = local.vault_enabled ? var.vault_namespace : null
  vault_service_name              = local.vault_enabled ? "vault" : null

  # Vault KMS 配置
  vault_kms_key_alias = "alias/${local.resource_prefix}-vault-unseal"

  # EKS 配置
  eks_cluster_name_valid = can(regex("^[a-zA-Z][a-zA-Z0-9-]*[a-zA-Z0-9]$", var.cluster_name))

  # 節點群組配置驗證
  node_groups_valid = alltrue([
    for name, config in var.node_groups : (
      config.min_capacity >= 0 &&
      config.max_capacity >= config.min_capacity &&
      config.desired_capacity >= config.min_capacity &&
      config.desired_capacity <= config.max_capacity
    )
  ])

  # ============================================================================
  # 服務配置 - 所有服務的啟用狀態和命名管理
  # ============================================================================

  # 服務啟用狀態
  services_enabled = {
    vpc                          = true
    eks                          = true
    rds                          = true
    elasticache                  = true
    kms                          = true
    secrets                      = true
    acm                          = true
    aws_load_balancer_controller = var.enable_aws_load_balancer_controller
    cluster_autoscaler           = var.enable_cluster_autoscaler
    external_dns                 = var.enable_external_dns
    external_secrets             = var.enable_external_secrets
    metrics_server               = var.enable_metrics_server
    reloader                     = var.enable_reloader
    vault                        = local.vault_enabled
  }

  # 標準化服務命名
  service_names = {
    vpc                = "${local.resource_prefix}-vpc"
    eks                = local.resource_prefix
    rds                = "${local.resource_prefix}-pg"
    elasticache        = "${local.resource_prefix}-cache"
    kms                = "${local.resource_prefix}-kms"
    secrets            = "${local.resource_prefix}-secrets"
    acm                = "${local.resource_prefix}-cert"
    load_balancer      = "${local.resource_prefix}-alb-controller"
    cluster_autoscaler = "${local.resource_prefix}-cluster-autoscaler"
    external_dns       = "${local.resource_prefix}-external-dns"
    external_secrets   = "${local.resource_prefix}-external-secrets"
    metrics_server     = "${local.resource_prefix}-metrics-server"
    reloader           = "${local.resource_prefix}-reloader"
    vault              = "${local.resource_prefix}-vault"
    state_lock_table   = "${local.resource_prefix}-terraform-state-lock"
  }

  # IAM 角色命名
  iam_role_names = {
    eks_cluster                  = "${local.resource_prefix}-cluster-role"
    eks_node_group               = "${local.resource_prefix}-node-group-role"
    aws_load_balancer_controller = "${local.resource_prefix}-alb-controller-role"
    cluster_autoscaler           = "${local.resource_prefix}-cluster-autoscaler-role"
    external_dns                 = "${local.resource_prefix}-external-dns-role"
    external_secrets             = "${local.resource_prefix}-external-secrets-role"
    vault                        = "${local.resource_prefix}-vault-role"
    github_actions               = "${local.resource_prefix}-github-actions"
  }

  # 安全群組命名
  security_group_names = {
    eks_cluster    = "${local.resource_prefix}-cluster-sg"
    eks_node_group = "${local.resource_prefix}-node-group-sg"
    rds            = "${local.resource_prefix}-rds-sg"
    elasticache    = "${local.resource_prefix}-cache-sg"
    alb            = "${local.resource_prefix}-alb-sg"
  }

  # ============================================================================
  # 標籤驗證和合規性檢查
  # ============================================================================

  # 必要標籤檢查
  required_tags = ["Environment", "Project", "Application", "Owner", "ManagedBy"]
  missing_required_tags = [
    for tag in local.required_tags : tag
    if !contains(keys(local.common_tags), tag)
  ]

  # 標籤值驗證
  tag_validation_errors = compact([
    # 環境標籤驗證
    !contains(["dev", "staging", "prod"], local.common_tags["Environment"]) ?
    "Environment 標籤值必須是 dev、staging 或 prod" : "",

    # 資料分類驗證
    !contains(["public", "internal", "confidential", "restricted"], local.common_tags["DataClassification"]) ?
    "DataClassification 標籤值必須是 public、internal、confidential 或 restricted" : "",

    # 安全級別驗證
    !contains(["low", "medium", "high", "critical"], local.common_tags["SecurityLevel"]) ?
    "SecurityLevel 標籤值必須是 low、medium、high 或 critical" : "",

    # 監控級別驗證
    !contains(["basic", "standard", "enhanced", "critical"], local.common_tags["MonitoringLevel"]) ?
    "MonitoringLevel 標籤值必須是 basic、standard、enhanced 或 critical" : "",
  ])

  # 標籤合規性摘要
  tag_compliance_summary = {
    total_tags          = length(local.common_tags)
    required_tags_count = length(local.required_tags)
    missing_tags_count  = length(local.missing_required_tags)
    validation_errors   = length(local.tag_validation_errors)
    compliance_score = length(local.missing_required_tags) == 0 && length(local.tag_validation_errors) == 0 ? 100 : (
      (length(local.required_tags) - length(local.missing_required_tags)) * 100 / length(local.required_tags)
    )
    is_compliant = length(local.missing_required_tags) == 0 && length(local.tag_validation_errors) == 0
  }

  # 向後相容性 - 保留 name_prefix 以避免破壞現有引用
  name_prefix = local.resource_prefix

  # ============================================================================
  # 驗證和錯誤處理 - 配置驗證、錯誤收集和狀態摘要
  # ============================================================================

  # 錯誤訊息收集
  validation_errors = compact([
    !local.vpc_cidr_valid ? "VPC CIDR 格式無效" : "",
    !local.private_subnets_valid ? "私有子網 CIDR 格式無效" : "",
    !local.public_subnets_valid ? "公有子網 CIDR 格式無效" : "",
    !local.eks_cluster_name_valid ? "EKS 叢集名稱格式無效" : "",
    !local.node_groups_valid ? "節點群組配置無效" : "",
    !local.allowed_cidr_blocks_valid ? "允許的 CIDR 區塊格式無效" : "",
    # Vault 相關錯誤檢查將在模組層級處理
  ])

  # 配置摘要
  configuration_summary = {
    cluster_name   = var.cluster_name
    region         = local.aws_region
    vpc_cidr       = local.vpc_cidr_block
    services_count = length([for k, v in local.services_enabled : k if v])
    vault_enabled  = local.vault_enabled
    has_errors     = length(local.validation_errors) > 0
    error_count    = length(local.validation_errors)

    # 新增的摘要資訊
    enabled_services = [for k, v in local.services_enabled : k if v]
    total_subnets    = length(var.private_subnets) + length(var.public_subnets)
    vault_namespace  = local.vault_namespace

    # 標籤合規性資訊
    tag_compliance = local.tag_compliance_summary
    missing_tags   = local.missing_required_tags
    tag_errors     = local.tag_validation_errors
  }
}
