# ============================================================================
# Terraform State Locking 配置
# ============================================================================
# 此文件定義用於 Terraform state locking 的 DynamoDB 表
# 確保多人協作時的狀態文件安全性

# DynamoDB 表用於 Terraform state locking
resource "aws_dynamodb_table" "terraform_state_lock" {
  name         = local.service_names.state_lock_table
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "LockID"

  attribute {
    name = "LockID"
    type = "S"
  }

  # 啟用時間點恢復
  point_in_time_recovery {
    enabled = true
  }

  # 啟用伺服器端加密
  server_side_encryption {
    enabled = true
  }

  # 標籤
  tags = merge(local.common_tags, {
    Name        = local.service_names.state_lock_table
    Purpose     = "terraform-state-locking"
    Component   = "infrastructure"
    Description = "DynamoDB table for Terraform state locking"
  })

  lifecycle {
    prevent_destroy = true
  }
}

# 輸出 DynamoDB 表名稱，供 backend 配置使用
output "terraform_state_lock_table_name" {
  description = "DynamoDB 表名稱，用於 Terraform state locking"
  value       = aws_dynamodb_table.terraform_state_lock.name
}

output "terraform_state_lock_table_arn" {
  description = "DynamoDB 表 ARN，用於 Terraform state locking"
  value       = aws_dynamodb_table.terraform_state_lock.arn
}
